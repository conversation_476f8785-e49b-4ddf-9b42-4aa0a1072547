<?php
/*
 * 简化版审核不通过页面模块 - 用于调试
 */
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$pagename = '审核不通过';
$pageurl = '?mod=rejected_simple';
$tempfile = 'rejected_simple.html';

echo "<!DOCTYPE HTML>
<html>
<head>
<title>审核不通过 - 测试页面</title>
<meta charset='utf-8'>
</head>
<body>
<h1>审核不通过页面测试</h1>
<p>如果您看到这个页面，说明模块文件可以正常执行。</p>";

// 测试数据库连接
try {
    $table = $DB->table('websites');
    $count = $DB->get_count($table, array('web_status' => 4));
    echo "<p>审核不通过网站数量: {$count}</p>";
    
    // 获取一些测试数据
    $websites = $DB->fetch_all("SELECT web_id, web_name, web_intro FROM {$table} WHERE web_status=4 LIMIT 5");
    
    if (count($websites) > 0) {
        echo "<h2>审核不通过网站列表:</h2>";
        echo "<ul>";
        foreach ($websites as $site) {
            echo "<li>{$site['web_name']} (ID: {$site['web_id']})</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>没有找到审核不通过的网站</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>数据库错误: " . $e->getMessage() . "</p>";
}

echo "<p><a href='?mod=rejected'>返回正式页面</a></p>";
echo "</body></html>";

// 不使用Smarty模板，直接输出
exit;
?>
