
    <!-- 美化的页脚开始 -->
    <footer id="footer" class="modern-footer">
        <!-- 域名评级徽章 -->
        <div class="footer-badge">
            <a href="https://frogdr.com/95dir.com?utm_source=95dir.com" target="_blank" class="badge-link">
                <img src="https://frogdr.com/95dir.com/badge-white.svg"
                     alt="Monitor your Domain Rating with FrogDR"
                     width="250" height="54"
                     class="rating-badge">
            </a>
        </div>

        <!-- 主要内容区域 -->
        <div class="footer-content">
            <!-- 导航链接区域 -->
            <div class="footer-nav">
                <div class="nav-section">
                    <h4>网站导航</h4>
                    <div class="nav-links">
                        {#foreach from=get_pages() item=item#}
                        <a href="{#$item.page_link#}" title="{#$item.page_name#}">{#$item.page_name#}</a>
                        {#/foreach#}
                        <a href="?mod=update" title="最新收录网站">最新收录</a>
                        <a href="?mod=archives" title="网站数据归档">数据归档</a>
                        <a href="?mod=top" title="热门网站排行榜">TOP排行榜</a>
                    </div>
                </div>

                <div class="nav-section">
                    <h4>服务功能</h4>
                    <div class="nav-links">
                        <a href="?mod=blacklist" title="违规网站黑名单">黑名单</a>
                        <a href="?mod=datastats" title="网站数据统计">数据公示</a>
                        <a href="?mod=sitemap" title="网站地图">站点地图</a>
                        <a href="?mod=webdir" title="网站目录分类浏览">网站目录</a>
                        <a href="?mod=vip_list" title="VIP优质网站推荐">VIP网站</a>
                        <a href="?mod=article" title="站长资讯文章">站长资讯</a>
                    </div>
                </div>

                <div class="nav-section">
                    <h4>联系我们</h4>
                    <div class="contact-info">
                        <div class="qq-group">
                            <a href="https://qun.qq.com/universal-share/share?ac=1&authKey=VD1VzovGfKEgsMPHwCRCMu6An9Er9ihrNfC88vO3Vkf0YgWW6O2OUZ6rQcFXnfXi&busi_data=eyJncm91cENvZGUiOiI4Njg4NTAyMTYiLCJ0b2tlbiI6IjZlcDhhc0srbGtNRlNrWjRkK3pVazA3blR0OGlUditUWGhYdldYdUI5N3M2VFF3dU0vMTBCSWl0c09relVZNUEiLCJ1aW4iOiIzNjMyMDk0In0=&data=3-2hnd4BuWS_RPz79Qa7DgA98Eag27Uvx7rVtiXfpes_7LoAlc5BtxTTkos3JyBmIcVTHp9ZC0K6p3qVXmJvYt__wRyd070zIKdMxpWzS_0&svctype=5&tempid=h5_group_info" target="_blank" class="qq-link">
                                <span class="qq-icon">🐧</span>
                                <span>QQ群：868850216</span>
                            </a>
                        </div>
                        <div class="stats-info">
                            <div class="stat-item">
                                <span class="stat-label">当前在线：</span>
                                <span id="onlineCount" class="stat-value">加载中...</span>
                                <span class="stat-unit">人</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">总访客：</span>
                                <span id="totalVisitors" class="stat-value">加载中...</span>
                                <span class="stat-unit">人</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 网站描述区域 -->
            <div class="footer-description">
                <div class="site-intro">
                    <h3>95目录网</h3>
                    <p>专业的网站分类目录平台，致力于为用户提供优质的网站收录服务。我们精心收录各行业优质网站，为用户提供便捷的网站发现和访问服务。</p>
                </div>
                <div class="keywords">
                    <span class="keywords-label">关键词：</span>
                    <span class="keywords-list">网站目录、网站收录、分类目录、网站推荐、优质网站、VIP网站、站长资讯、网站分类、网站大全、免费收录</span>
                </div>
            </div>
        </div>

        <!-- 版权信息区域 -->
        <div class="footer-bottom">
            <div class="copyright-info">
                <div class="copyright-text">
                    {#$site_copyright#} |
                    <a href="https://beian.miit.gov.cn/#/Integrated/index" rel="nofollow" target="_blank" class="icp-link">鄂ICP备2024062716号-1</a>
                </div>
                <div class="runtime-info">
                    {#insert name="script_time"#}
                </div>
            </div>
        </div>
    </footer>

<script>
    // 更新在线统计
    function updateOnlineStats() {
        // 添加时间戳防止缓存
        const timestamp = new Date().getTime();
        const url = '/data/online_stats/online.php?t=' + timestamp;

        fetch(url, {
            method: 'GET',
            headers: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('HTTP ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            // 确保数据有效
            const online = parseInt(data.online) || 0;
            const total = parseInt(data.total) || 0;

            document.getElementById('onlineCount').textContent = online;
            document.getElementById('totalVisitors').textContent = total.toLocaleString();

            // 调试信息（可选）
            console.log('在线统计更新:', { online: online, total: total });
        })
        .catch(error => {
            console.error('在线统计服务错误:', error);
            document.getElementById('onlineCount').textContent = '加载中...';
            document.getElementById('totalVisitors').textContent = '加载中...';
        });
    }

    // 页面加载时立即执行
    document.addEventListener('DOMContentLoaded', function() {
        updateOnlineStats();
    });

    // 设置定时器 - 在线统计每60秒更新
    setInterval(updateOnlineStats, 60000);

    // 页面获得焦点时也更新一次
    window.addEventListener('focus', updateOnlineStats);
</script>