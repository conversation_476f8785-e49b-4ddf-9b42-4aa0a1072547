<?php
/* Smarty version 4.5.5, created on 2025-07-26 19:18:13
  from '/www/wwwroot/www.95dir.com/themes/system/website.html' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.5.5',
  'unifunc' => 'content_6884b9753071f9_76677841',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '14ff7b02ad4f63630e775d38dfe06953e4c2e5c9' => 
    array (
      0 => '/www/wwwroot/www.95dir.com/themes/system/website.html',
      1 => 1752343366,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:header.html' => 1,
    'file:footer.html' => 1,
  ),
),false)) {
function content_6884b9753071f9_76677841 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender("file:header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

	<?php if ($_smarty_tpl->tpl_vars['action']->value == 'list') {?>
    <h3 class="title"><em><?php echo $_smarty_tpl->tpl_vars['pagetitle']->value;?>
</em><span><a href="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
?act=add">+添加新站点</a></span></h3>
	<div class="listbox">
		<form name="mform" method="post" action="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">
		<div class="search">
			<input name="keywords" type="text" id="keywords" class="ipt" size="30" value="<?php echo $_smarty_tpl->tpl_vars['keywords']->value;?>
" />
			<input type="submit" class="btn" value="搜索" />
        </div>
        </form>
                    
		<form name="mform" method="post" action="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">
		<div class="toolbar">
			<select name="act" id="act" class="sel">
			<option value="del" style="color: #f00;">删除选定</option>
            <option value="move" style="color: #06c;">移动内容</option>
            <option value="attr" style="color: #f60;">属性设置</option>
			</select>
			<input type="submit" class="btn" value="应用" onClick="if(IsCheck('web_id[]')==false){alert('请指定您要操作的站点ID！');return false;}else{return confirm('确认执行此操作吗？');}">
			<select class="sel" onChange="if(this.options[this.selectedIndex].value!=''){location='<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
?status='+this.options[this.selectedIndex].value+'&cate_id=<?php echo $_smarty_tpl->tpl_vars['cate_id']->value;?>
&sort=<?php echo $_smarty_tpl->tpl_vars['sort']->value;?>
&order=<?php echo $_smarty_tpl->tpl_vars['order']->value;
echo $_smarty_tpl->tpl_vars['key_url']->value;?>
';}">
			<option value="0">所有状态</option>
			<option value="1" style="color: #333;"<?php echo opt_selected($_smarty_tpl->tpl_vars['status']->value,1);?>
>拉黑</option>
			<option value="2" style="color: #f30;"<?php echo opt_selected($_smarty_tpl->tpl_vars['status']->value,2);?>
>待审核</option>
			<option value="3" style="color: #080;"<?php echo opt_selected($_smarty_tpl->tpl_vars['status']->value,3);?>
>已审核</option>
			<option value="4" style="color: #f60;"<?php echo opt_selected($_smarty_tpl->tpl_vars['status']->value,4);?>
>审核不通过</option>
			</select>
			<select class="sel" onChange="if(this.options[this.selectedIndex].value!=''){location='<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
?status=<?php echo $_smarty_tpl->tpl_vars['status']->value;?>
&cate_id='+this.options[this.selectedIndex].value+'&sort=<?php echo $_smarty_tpl->tpl_vars['sort']->value;?>
&order=<?php echo $_smarty_tpl->tpl_vars['order']->value;
echo $_smarty_tpl->tpl_vars['key_url']->value;?>
';}">
			<option value="0" selected>所有分类</option>
			<?php echo $_smarty_tpl->tpl_vars['category_option']->value;?>

			</select>
			<select class="sel" onChange="if(this.options[this.selectedIndex].value!=''){location='<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
?status=<?php echo $_smarty_tpl->tpl_vars['status']->value;?>
&cate_id=<?php echo $_smarty_tpl->tpl_vars['cate_id']->value;?>
&sort='+this.options[this.selectedIndex].value+'<?php echo $_smarty_tpl->tpl_vars['key_url']->value;?>
';}">
			<option value="1"<?php echo opt_selected($_smarty_tpl->tpl_vars['sort']->value,1);?>
>按提交时间排列</option>
			<option value="2"<?php echo opt_selected($_smarty_tpl->tpl_vars['sort']->value,2);?>
>按百度收录排列</option>
            <option value="3"<?php echo opt_selected($_smarty_tpl->tpl_vars['sort']->value,3);?>
>按必应收录排列</option>
            <option value="4"<?php echo opt_selected($_smarty_tpl->tpl_vars['sort']->value,4);?>
>按360收录排列</option>
			<option value="5"<?php echo opt_selected($_smarty_tpl->tpl_vars['sort']->value,5);?>
>按搜狗收录排列</option>
			<option value="6"<?php echo opt_selected($_smarty_tpl->tpl_vars['sort']->value,6);?>
>按入站排列</option>
			<option value="7"<?php echo opt_selected($_smarty_tpl->tpl_vars['sort']->value,7);?>
>按出站排列</option>
			<option value="8"<?php echo opt_selected($_smarty_tpl->tpl_vars['sort']->value,8);?>
>按浏览排列</option>
			<option value="9"<?php echo opt_selected($_smarty_tpl->tpl_vars['sort']->value,9);?>
>按错误排列</option>
			</select>
			<select class="sel" onChange="if(this.options[this.selectedIndex].value!=''){location='<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
?status=<?php echo $_smarty_tpl->tpl_vars['status']->value;?>
&cate_id=<?php echo $_smarty_tpl->tpl_vars['cate_id']->value;?>
&sort=<?php echo $_smarty_tpl->tpl_vars['sort']->value;?>
&order='+this.options[this.selectedIndex].value+'<?php echo $_smarty_tpl->tpl_vars['key_url']->value;?>
';}">
			<option value="DESC"<?php echo opt_selected($_smarty_tpl->tpl_vars['order']->value,'DESC');?>
>降序</option>
			<option value="ASC"<?php echo opt_selected($_smarty_tpl->tpl_vars['order']->value,'ASC');?>
>升序</option>
			</select>
		</div>
		
    	<table width="100%" border="0" cellspacing="1" cellpadding="0">
			<tr>
				<th><input type="checkbox" id="ChkAll" onClick="CheckAll(this.form)"></th>
				<th>ID</th>
				<th>所属分类</th>
				<th>网站名称</th>
				<th>百度收录</th>
                <th>必应收录</th>
                <th>360收录</th>
				<th>搜狗收录</th>
				<th>入站次数</th>
				<th>出站次数</th>
				<th>浏览次数</th>
				<th>属性状态</th>
				<th>收录时间</th>
				<th>操作选项</th>
			</tr>
			<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['websites']->value, 'web');
$_smarty_tpl->tpl_vars['web']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['web']->value) {
$_smarty_tpl->tpl_vars['web']->do_else = false;
?>
			<tr>
				<td><input name="web_id[]" type="checkbox" value="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_id'];?>
"></td>
				<td><?php echo $_smarty_tpl->tpl_vars['web']->value['web_id'];?>
</td>
				<td><?php echo $_smarty_tpl->tpl_vars['web']->value['web_cate'];?>
</td>
				<td class="ltext"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
</td>
				<td class="data"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_grank'];?>
</td>
                <td class="data"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_brank'];?>
</td>
                <td class="data"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_srank'];?>
</td>
				<td class="data"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_arank'];?>
</td>
				<td class="data"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_instat'];?>
</td>
				<td class="data"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_outstat'];?>
</td>
				<td class="data"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_views'];?>
</td>
				<td><?php echo $_smarty_tpl->tpl_vars['web']->value['web_attr'];?>
</td>
				<td><?php echo $_smarty_tpl->tpl_vars['web']->value['web_ctime'];?>
</td>
				<td><?php echo $_smarty_tpl->tpl_vars['web']->value['web_operate'];?>
</td>
			</tr>
			<?php
}
if ($_smarty_tpl->tpl_vars['web']->do_else) {
?>
			<tr><td colspan="14">无任何网站！</td></tr>
			<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
		</table>
        </form>
        <div class="pagebox"><?php echo $_smarty_tpl->tpl_vars['showpage']->value;?>
</div>
    </div>
    <?php }?>

	<?php if ($_smarty_tpl->tpl_vars['action']->value == 'add' || $_smarty_tpl->tpl_vars['action']->value == 'edit') {?>
    <h3 class="title"><em><?php echo $_smarty_tpl->tpl_vars['pagetitle']->value;?>
</em><span><a href="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">返回列表&raquo;</a></span></h3>
	<div class="formbox">
    	<form name="mform" method="post" action="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">
		<table width="100%" border="0" cellspacing="1" cellpadding="0">
			<tr>
				<th>所属分类：</th>
				<td><select name="cate_id" id="cate_id" class="sel"><?php echo $_smarty_tpl->tpl_vars['category_option']->value;?>
</select></td>
			</tr>
			<tr>
    <th>网站名称：</th>
    <td>
        <input name="web_name" type="text" class="ipt" id="web_name" size="50" maxlength="12" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['web_name'];?>
" onblur="checkWebName(this.value)" onkeyup="checkWebNameLength(this)" />
        <br><span id="web_name_msg" style="color: #999; font-size: 12px;">最多12个字符（6个汉字）</span>
    </td>
</tr>
<tr>
    <th>网站域名：</th>
    <td>
        <input name="web_url" type="text" class="ipt" id="web_url" size="50" maxlength="255" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['web_url'];?>
" onblur="safeCheckAdminUrl(this.value)" />
        <input type="button" class="btn" id="meta_btn" value="抓取Meta" onclick="GetMeta()">
        <br><span id="url_msg" style="color: #999; font-size: 12px;">请输入完整的域名，不包含http://</span>
        <span class="tips">例: www.95dir.com</span>
    </td>
</tr>
<tr>
    <th>TAG标签：</th>
    <td><input name="web_tags" type="text" class="ipt" id="web_tags" size="50" maxlength="255" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['web_tags'];?>
" onBlur="javascript:this.value=this.value.replace(/，/ig,',');" /><span class="tips">多个标签用英文的“,”逗号隔开</span></td>
</tr>
<tr>
    <th>网站截图：</th>
    <td>
        <input name="web_pic" type="text" class="ipt" id="web_pic" size="50" maxlength="100" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['web_pic'];?>
" />
        <input type="button" class="btn" id="logo_btn" value="获取网站LOGO" onclick="GetLogo()" />
        <span id="logo_status" style="margin-left: 10px;"></span>
    </td>
</tr>

<link rel="stylesheet" href="../public/css/logo-preview.css">
<?php echo '<script'; ?>
 type="text/javascript">
// 获取网站LOGO功能
function GetLogo() {
    const urlInput = document.getElementById('web_url');
    const logoBtn = document.getElementById('logo_btn');
    const logoStatus = document.getElementById('logo_status');
    const webPicInput = document.getElementById('web_pic');

    if (!urlInput || !urlInput.value) {
        alert('请先填写网站域名');
        return;
    }

    const url = urlInput.value.trim();
    if (!url.includes('.')) {
        logoStatus.innerHTML = '<span style="color:red;">请输入有效的域名（如：www.example.com）</span>';
        return;
    }

    // 设置加载状态
    logoBtn.disabled = true;
    logoStatus.innerHTML = '正在获取LOGO... <span style="display:inline-block;width:16px;height:16px;border:2px solid #ccc;border-top-color:#3498db;border-radius:50%;animation:spin 1s linear infinite;"></span>';

    // 发送请求
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 增加到15秒

    fetch('get_logo.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: 'url=' + encodeURIComponent(url),
        signal: controller.signal
    })
    .then(response => {
        clearTimeout(timeoutId);
        
        // 检查响应类型
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('服务器返回了非JSON响应，可能是PHP错误');
        }
        
        if (!response.ok) {
            throw new Error(`HTTP错误: ${response.status}`);
        }
        
        return response.text(); // 先获取文本
    })
    .then(text => {
        console.log('服务器响应:', text); // 调试信息
        
        try {
            const data = JSON.parse(text);
            handleResponse(data);
        } catch (e) {
            console.error('JSON解析错误:', e);
            console.error('响应内容:', text);
            throw new Error('服务器返回了无效的JSON格式');
        }
    })
    .catch(error => {
        clearTimeout(timeoutId);
        console.error('请求错误:', error);
        
        let errorMessage = '请求失败: ';
        if (error.name === 'AbortError') {
            errorMessage += '请求超时';
        } else {
            errorMessage += error.message;
        }
        
        logoStatus.innerHTML = `<span style="color:red;">${errorMessage}</span>`;
    })
    .finally(() => {
        logoBtn.disabled = false;
    });

    function handleResponse(data) {
        if (data.status === 'success') {
            webPicInput.value = data.logo_path;

            let successMsg = '<span style="color:green;">获取成功! ';

            // 显示图片信息
            if (data.is_svg) {
                successMsg += '(SVG矢量图)';
            } else {
                successMsg += `(${data.width}×${data.height})`;
            }

            // 显示文件大小
            const sizeKB = Math.round(data.file_size / 1024);
            successMsg += ` ${sizeKB}KB`;

            // 显示优先级信息
            if (data.is_priority) {
                successMsg += ' <span style="color:#ff6b35;">★优质Logo</span>';
            }

            // 显示质量评分
            if (data.quality_score > 80) {
                successMsg += ' <span style="color:#28a745;">高质量</span>';
            } else if (data.quality_score > 50) {
                successMsg += ' <span style="color:#ffc107;">中等质量</span>';
            }

            successMsg += '</span>';
            logoStatus.innerHTML = successMsg;

            // 添加详细信息
            const detailDiv = document.createElement('div');
            detailDiv.style.marginTop = '3px';
            detailDiv.style.fontSize = '11px';
            detailDiv.style.color = '#666';
            detailDiv.innerHTML = `来源: ${data.source_url.length > 50 ? data.source_url.substring(0, 50) + '...' : data.source_url}`;
            logoStatus.appendChild(detailDiv);

            // 添加预览图片
            const previewDiv = document.createElement('div');
            previewDiv.style.marginTop = '5px';
            previewDiv.className = 'logo-bg-container'; // 直接添加渐变背景类

            const img = document.createElement('img');
            img.src = 'https://www.95dir.com/' + data.preview_path;
            img.style.maxWidth = '100px';
            img.style.maxHeight = '50px';
            img.style.border = '1px solid #ddd';
            img.style.padding = '2px';
            img.style.borderRadius = '3px';
            img.alt = 'Logo Preview';

            img.onerror = function() {
                this.style.display = 'none';
                previewDiv.innerHTML = '<span style="color: orange;">预览加载失败</span>';
            };

            previewDiv.appendChild(img);
            logoStatus.appendChild(previewDiv);
        } else {
            let errorMsg = `<span style="color:red;">获取失败: ${data.message}</span>`;

            // 显示调试信息（如果有）
            if (data.debug && data.debug.total_candidates) {
                errorMsg += `<br><small style="color:#666;">已尝试 ${data.debug.total_candidates} 个候选URL</small>`;
            }

            logoStatus.innerHTML = errorMsg;
        }
    }
}
<?php echo '</script'; ?>
>

<tr>
    <th valign="top">网站简介：</th>
    <td><textarea name="web_intro" cols="55" rows="8" class="ipt" id="web_intro"><?php echo $_smarty_tpl->tpl_vars['row']->value['web_intro'];?>
</textarea></td>
</tr>
<tr>
    <th>网站AI简介：</th>
    <td>
        <?php echo '<script'; ?>
 type="text/javascript" src="path/to/kindeditor/kindeditor-all-min.js" data-ke-src="path/to/kindeditor/kindeditor-all-min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 type="text/javascript">
var editor;
        KindEditor.ready(function(K) {
            editor = K.create('textarea[name="web_ai_intro"]', {
                resizeType: 1,
                allowPreviewEmoticons: false,
                allowImageUpload: true,
                uploadJson: 'upload.php?act=upload', // 图片上传接口
                escapeMode: false, // 关闭 HTML 转义，防止链接被转义成 &quot;
                filterMode: false, // 关闭 HTML 过滤
                items: [
                    'source', '|', 'fontname', 'fontsize', '|', 'forecolor', 'hilitecolor', 'bold', 'italic', 'underline',
                    'removeformat', '|', 'justifyleft', 'justifycenter', 'justifyright', 'insertorderedlist',
                    'insertunorderedlist', '|', 'emoticons', 'image', 'link', '|', 'fullscreen'
                ],
                afterCreate: function() {
                    this.sync(); // 确保编辑器内容与 textarea 同步
                },
                afterChange: function() {
                    this.sync(); // 内容变化时同步
                }
            });
        });


            // AJAX请求智谱AI生成简介
            function generateAIIntro() {
                var url = document.getElementById('web_url').value;
                var tags = document.getElementById('web_tags').value;
                var intro = document.getElementById('web_intro').value;

                if (!url || !tags || !intro) {
                    alert('请填写网站域名、TAG标签和网站简介');
                    return;
                }

                // 显示加载中提示
                document.getElementById('generate_status').innerText = '正在生成中...';

                // 使用AJAX发送请求到后端
                var xhr = new XMLHttpRequest();
                xhr.open('POST', 'generate_intro.php', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        if (xhr.status === 200) {
                            try {
                                var response = JSON.parse(xhr.responseText);
                            if (response.status === 'success') {
                                // 将生成的简介填入编辑器
                                editor.html(response.content);
                                editor.sync(); // 同步到textarea
                                document.getElementById('generate_status').innerText = '生成成功！';
                                // 显示调试信息（可选）
                                if (response.debug) {
                                    console.log('调试信息:', response.debug);
                                }
                            } else {
                                document.getElementById('generate_status').innerText = '生成失败：' + response.message;
                                // 显示详细错误信息
                                if (response.debug) {
                                    console.log('错误调试信息:', response.debug);
                                    document.getElementById('generate_status').innerText += ' (详细信息请查看浏览器控制台)';
                                }
                            }
                            } catch (e) {
                                document.getElementById('generate_status').innerText = 'JSON解析错误：' + e.message;
                                console.log('JSON解析错误:', e);
                                console.log('原始响应:', xhr.responseText);
                            }
                        } else {
                            document.getElementById('generate_status').innerText = '请求错误(' + xhr.status + ')，请重试';
                            console.log('HTTP错误:', xhr.status, xhr.statusText);
                            console.log('响应内容:', xhr.responseText);
                        }
                    }
                };
                xhr.send('url=' + encodeURIComponent(url) + '&tags=' + encodeURIComponent(tags) + '&intro=' + encodeURIComponent(intro));
            }
        <?php echo '</script'; ?>
>
        
        <textarea name="web_ai_intro" id="web_ai_intro" cols="50" rows="6" class="ipt" style="width: 600px; height: 400px; visibility: hidden;"><?php echo $_smarty_tpl->tpl_vars['row']->value['web_ai_intro'];?>
</textarea>
        <div style="margin-bottom: 10px;">
            <button type="button" class="btn" onclick="generateAIIntro()">生成AI简介</button>
            <span id="generate_status" style="margin-left: 10px;"></span>
        </div>
    </td>
</tr>
            <tr>
				<th>服务器IP：</th>
				<td><input name="web_ip" type="text" class="ipt" id="web_ip" size="30" maxlength="30" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['web_ip'];?>
" /><input type="button" class="btn" id="data_btn" value="获取数据" onclick="GetData()"><span class="tips">例: 127.0.0.1</span></td>
			</tr>
 			<tr>
				<th>百度收录量：</th>
				<td><input name="web_grank" type="text" class="ipt" id="web_grank" size="10" maxlength="10" value="<?php echo !$_smarty_tpl->tpl_vars['row']->value['web_grank'] ? '0' : $_smarty_tpl->tpl_vars['row']->value['web_grank'];?>
" /> 条</td>
			</tr>
 			<tr>
				<th>必应收录量：</th>
				<td><input name="web_brank" type="text" class="ipt" id="web_brank" size="10" maxlength="10" value="<?php echo !$_smarty_tpl->tpl_vars['row']->value['web_brank'] ? '0' : $_smarty_tpl->tpl_vars['row']->value['web_brank'];?>
" /> 条</td>
			</tr>
 			<tr>
				<th>360收录量：</th>
				<td><input name="web_srank" type="text" class="ipt" id="web_srank" size="10" maxlength="10" value="<?php echo !$_smarty_tpl->tpl_vars['row']->value['web_srank'] ? '0' : $_smarty_tpl->tpl_vars['row']->value['web_srank'];?>
" /> 条</td>
			</tr>
 			<tr>
				<th>搜狗收录量：</th>
				<td><input name="web_arank" type="text" class="ipt" id="web_arank" size="10" maxlength="10" value="<?php echo !$_smarty_tpl->tpl_vars['row']->value['web_arank'] ? '0' : $_smarty_tpl->tpl_vars['row']->value['web_arank'];?>
" /> 条</td>
			</tr>
 			<tr>
				<th>点入次数：</th>
				<td><input name="web_instat" type="text" class="ipt" id="web_instat" size="10" maxlength="10" value="<?php echo !$_smarty_tpl->tpl_vars['row']->value['web_instat'] ? '0' : $_smarty_tpl->tpl_vars['row']->value['web_instat'];?>
" /> 次</td>
			</tr>
 			<tr>
				<th>点出次数：</th>
				<td><input name="web_outstat" type="text" class="ipt" id="web_outstat" size="10" maxlength="10" value="<?php echo !$_smarty_tpl->tpl_vars['row']->value['web_outstat'] ? '0' : $_smarty_tpl->tpl_vars['row']->value['web_outstat'];?>
" /> 次</td>
			</tr>
 			<tr>
				<th>浏览次数：</th>
				<td><input name="web_views" type="text" class="ipt" id="web_views" size="10" maxlength="10" value="<?php echo !$_smarty_tpl->tpl_vars['row']->value['web_views'] ? '0' : $_smarty_tpl->tpl_vars['row']->value['web_views'];?>
" /> 次</td>
			</tr>
			<tr>
				<th>错误次数：</th>
				<td><input name="web_errors" type="text" class="ipt" id="web_errors" size="10" maxlength="10" value="<?php echo !$_smarty_tpl->tpl_vars['row']->value['web_errors'] ? '0' : $_smarty_tpl->tpl_vars['row']->value['web_errors'];?>
" /> 次</td>
			</tr>
			<tr>
				<th>属性设置：</th>
				<td><input name="web_ispay" type="checkbox" id="web_ispay" value="1"<?php echo opt_checked($_smarty_tpl->tpl_vars['ispay']->value,1);?>
 /><label for="web_ispay">付费</label>　<input name="web_istop" type="checkbox" id="web_istop" value="1"<?php echo opt_checked($_smarty_tpl->tpl_vars['istop']->value,1);?>
 /><label for="web_istop">置顶</label>　<input name="web_isbest" type="checkbox" id="web_isbest" value="1"<?php echo opt_checked($_smarty_tpl->tpl_vars['isbest']->value,1);?>
 /><label for="web_isbest">推荐</label></td>
			</tr>
			<tr>
<th>审核状态：</th>
<td><select name="web_status" id="web_status" class="sel" onchange="toggleStatusFields()">
    <option value="1" style="color: #333;"<?php echo opt_selected($_smarty_tpl->tpl_vars['status']->value,1);?>
>黑名单</option>
    <option value="2" style="color: #f30;"<?php echo opt_selected($_smarty_tpl->tpl_vars['status']->value,2);?>
>待审核</option>
    <option value="3" style="color: #080;"<?php echo opt_selected($_smarty_tpl->tpl_vars['status']->value,3);?>
>已审核</option>
    <option value="4" style="color: #f00;"<?php echo opt_selected($_smarty_tpl->tpl_vars['status']->value,4);?>
>审核不通过</option>
</select>
<?php if ($_smarty_tpl->tpl_vars['action']->value == 'edit' || $_smarty_tpl->tpl_vars['action']->value == 'attr') {?>
    <!-- 黑名单理由和分类 -->
    <div id="blacklist_reason_div" style="display: <?php if ($_smarty_tpl->tpl_vars['status']->value == 1) {?>block<?php } else { ?>none<?php }?>; margin-top: 10px;">
        <label for="web_blacklist_category">黑名单分类：</label><br>
        <select name="web_blacklist_category" id="web_blacklist_category" class="sel" style="margin-bottom: 10px;">
            <option value="0"<?php if ($_smarty_tpl->tpl_vars['row']->value['web_blacklist_category'] == 0) {?> selected<?php }?>>其他</option>
            <option value="1"<?php if ($_smarty_tpl->tpl_vars['row']->value['web_blacklist_category'] == 1) {?> selected<?php }?>>违法违规</option>
            <option value="2"<?php if ($_smarty_tpl->tpl_vars['row']->value['web_blacklist_category'] == 2) {?> selected<?php }?>>色情内容</option>
            <option value="3"<?php if ($_smarty_tpl->tpl_vars['row']->value['web_blacklist_category'] == 3) {?> selected<?php }?>>赌博博彩</option>
            <option value="4"<?php if ($_smarty_tpl->tpl_vars['row']->value['web_blacklist_category'] == 4) {?> selected<?php }?>>诈骗欺诈</option>
            <option value="5"<?php if ($_smarty_tpl->tpl_vars['row']->value['web_blacklist_category'] == 5) {?> selected<?php }?>>恶意软件</option>
            <option value="6"<?php if ($_smarty_tpl->tpl_vars['row']->value['web_blacklist_category'] == 6) {?> selected<?php }?>>垃圾信息</option>
            <option value="7"<?php if ($_smarty_tpl->tpl_vars['row']->value['web_blacklist_category'] == 7) {?> selected<?php }?>>版权侵权</option>
            <option value="8"<?php if ($_smarty_tpl->tpl_vars['row']->value['web_blacklist_category'] == 8) {?> selected<?php }?>>政治敏感</option>
        </select><br>
        <label for="web_blacklist_reason">拉黑理由：</label><br>
        <textarea name="web_blacklist_reason" id="web_blacklist_reason" cols="50" rows="3" class="ipt" placeholder="请详细说明拉黑原因..."><?php echo $_smarty_tpl->tpl_vars['row']->value['web_blacklist_reason'];?>
</textarea>
    </div>
    <!-- 审核不通过原因 -->
    <div id="reject_reason_div" style="display: <?php if ($_smarty_tpl->tpl_vars['status']->value == 4) {?>block<?php } else { ?>none<?php }?>; margin-top: 10px;">
        <label for="reject_reason">未通过原因：</label><br>
        <textarea name="reject_reason" id="reject_reason" cols="50" rows="3" class="ipt"><?php echo $_smarty_tpl->tpl_vars['reject_reason']->value;?>
</textarea>
    </div>
<?php }?>
</td>
</tr>
			<tr class="btnbox">
            	<th>&nbsp;</th>
				<td>
					<input name="act" type="hidden" id="act" value="<?php echo $_smarty_tpl->tpl_vars['h_action']->value;?>
">
					<?php if ($_smarty_tpl->tpl_vars['action']->value == 'edit' && $_smarty_tpl->tpl_vars['row']->value['web_id']) {?>
					<input name="web_id" type="hidden" id="web_id" value="<?php echo $_smarty_tpl->tpl_vars['row']->value['web_id'];?>
">
					<?php }?>
					<input type="submit" class="btn" value="保 存">&nbsp;
					<input type="reset" class="btn" value="取 消" onClick="window.location.href='<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
';">
				</td>
			</tr>
		</table>
        </form>
	</div>
	<?php }?>
	

<?php echo '<script'; ?>
>
        // DOM加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            const urlInput = document.getElementById('web_url');
            
            // 实时输入处理
            urlInput.addEventListener('input', function(e) {
                this.value = sanitizeURL(this.value);
            });

            // 失焦时最终校验
            urlInput.addEventListener('blur', function(e) {
                this.value = finalCheckURL(this.value);
            });
        });

        // 即时清理函数
        function sanitizeURL(url) {
            return url
                .replace(/^\s+/, '')         // 去除首部空格
                .replace(/^(https?|ftp):\/\//i,'')  // 移除协议头
                .replace(/^\/+/g, '')        // 移除开头的斜杠
                .replace(/\s+/g, '')         // 移除所有空格
                .replace(/\/+/g, '/');       // 合并多余斜杠
        }

        // 最终校验函数
        function finalCheckURL(url) {
            const cleaned = sanitizeURL(url)
                .replace(/\/+$/g, '')        // 去除末尾斜杠
                .trim();

            // 添加自定义校验逻辑（示例）
            if (!cleaned) {
                console.warn('URL不能为空');
            }

            return cleaned;
        }

        // 切换状态字段显示
        function toggleStatusFields() {
            const statusSelect = document.getElementById('web_status');
            const blacklistDiv = document.getElementById('blacklist_reason_div');
            const rejectDiv = document.getElementById('reject_reason_div');

            if (statusSelect && blacklistDiv && rejectDiv) {
                const status = statusSelect.value;

                // 显示/隐藏黑名单理由
                blacklistDiv.style.display = (status == '1') ? 'block' : 'none';

                // 显示/隐藏审核不通过原因
                rejectDiv.style.display = (status == '4') ? 'block' : 'none';
            }
        }
    <?php echo '</script'; ?>
>
	
	<?php if ($_smarty_tpl->tpl_vars['action']->value == 'move') {?>
    <h3 class="title"><em><?php echo $_smarty_tpl->tpl_vars['pagetitle']->value;?>
</em><span><a href="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">返回列表&raquo;</a></span></h3>
	<div class="formbox">
		<form name="mform" method="post" action="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">
		<table width="100%" border="0" cellspacing="1" cellpadding="0">
			<tr>
				<th valign="top">已选定的内容：</th>
				<td><?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['websites']->value, 'item');
$_smarty_tpl->tpl_vars['item']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->do_else = false;
?><a href="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
&act=edit&web_id=<?php echo $_smarty_tpl->tpl_vars['item']->value['web_id'];?>
"><?php echo $_smarty_tpl->tpl_vars['item']->value['web_name'];?>
</a><input name="web_id[]" type="hidden" value="<?php echo $_smarty_tpl->tpl_vars['item']->value['web_id'];?>
"><br /><?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?></td>
			</tr>
			<tr>
				<th>将以上内容移动至：</th>
				<td><select name="cate_id" id="cate_id" class="sel"><?php echo $_smarty_tpl->tpl_vars['category_option']->value;?>
</select></td>
			</tr>
			<tr class="btnbox">
            	<th>&nbsp;</th>
				<td>
					<input name="act" type="hidden" id="act" value="<?php echo $_smarty_tpl->tpl_vars['h_action']->value;?>
">
					<input type="submit" class="btn" value="保 存">&nbsp;
					<input type="reset" class="btn" value="取 消" onClick="window.location.href='<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
';">
				</td>
			</tr>
		</table>
		</form>
	</div>
	<?php }?>
    
	<?php if ($_smarty_tpl->tpl_vars['action']->value == 'attr') {?>
    <h3 class="title"><em><?php echo $_smarty_tpl->tpl_vars['pagetitle']->value;?>
</em><span><a href="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">返回列表&raquo;</a></span></h3>
	<div class="formbox">
		<form name="mform" method="post" action="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">
		<table width="100%" border="0" cellspacing="1" cellpadding="0">
			<tr>
				<th valign="top">已选定的内容：</th>
				<td><?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['websites']->value, 'item');
$_smarty_tpl->tpl_vars['item']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->do_else = false;
echo $_smarty_tpl->tpl_vars['item']->value['web_id'];?>
 - <a href="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
&act=edit&web_id=<?php echo $_smarty_tpl->tpl_vars['item']->value['web_id'];?>
"><?php echo $_smarty_tpl->tpl_vars['item']->value['web_name'];?>
</a><input name="web_id[]" type="hidden" value="<?php echo $_smarty_tpl->tpl_vars['item']->value['web_id'];?>
"><br /><?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?></td>
			</tr>
			<tr>
				<th>属性设置：</th>
				<td><input name="web_ispay" type="checkbox" id="web_ispay" value="1" /><label for="web_ispay">付费</label> <input name="web_istop" type="checkbox" id="web_istop" value="1" /><label for="web_istop">置顶</label>　<input name="web_isbest" type="checkbox" id="web_isbest" value="1" /><label for="web_isbest">推荐</label></td>
			</tr>
			<tr>
<th>审核状态：</th>
<td><select name="web_status" id="web_status" class="sel">
    <option value="1" style="color: #333;"<?php echo opt_selected($_smarty_tpl->tpl_vars['status']->value,1);?>
>黑名单</option>
    <option value="2" style="color: #f30;"<?php echo opt_selected($_smarty_tpl->tpl_vars['status']->value,2);?>
>待审核</option>
    <option value="3" style="color: #080;"<?php echo opt_selected($_smarty_tpl->tpl_vars['status']->value,3);?>
>已审核</option>
    <option value="4" style="color: #f00;"<?php echo opt_selected($_smarty_tpl->tpl_vars['status']->value,4);?>
>审核不通过</option>
</select>
<?php if ($_smarty_tpl->tpl_vars['action']->value == 'edit' || $_smarty_tpl->tpl_vars['action']->value == 'attr') {?>
    <div id="reject_reason_div" style="display: <?php if ($_smarty_tpl->tpl_vars['status']->value == 4) {?>block<?php } else { ?>none<?php }?>; margin-top: 10px;">
        <label for="reject_reason">未通过原因：</label><br>
        <textarea name="reject_reason" id="reject_reason" cols="50" rows="3" class="ipt"><?php echo $_smarty_tpl->tpl_vars['reject_reason']->value;?>
</textarea>
    </div>
<?php }?>
</td>
</tr>
			<tr class="btnbox">
            	<th>&nbsp;</th>
				<td colspan="2">
				<input name="act" type="hidden" id="act" value="<?php echo $_smarty_tpl->tpl_vars['h_action']->value;?>
">
				<input type="submit" class="btn" value="保 存">&nbsp;
				<input type="reset" class="btn" value="取 消" onClick="window.location.href='<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
';">
				</td>
			</tr>
		</table>
		</form>
	</div>
	<?php }?>
 <?php echo '<script'; ?>
>
document.addEventListener('DOMContentLoaded', function() {
    const statusSelect = document.getElementById('web_status');
    const rejectReasonDiv = document.getElementById('reject_reason_div');
    
    if (statusSelect && rejectReasonDiv) {
        statusSelect.addEventListener('change', function() {
            if (this.value == '4') {
                rejectReasonDiv.style.display = 'block';
            } else {
                rejectReasonDiv.style.display = 'none';
            }
        });
    }
});
<?php echo '</script'; ?>
>   
    <?php if ($_smarty_tpl->tpl_vars['action']->value == 'down') {?>
    <h3 class="title"><em><?php echo $_smarty_tpl->tpl_vars['pagetitle']->value;?>
</em><span><a href="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">返回列表&raquo;</a></span></h3>
	<div class="formbox">
		<form name="mform" method="post" action="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">
		<table width="100%" border="0" cellspacing="1" cellpadding="0">
			<tr>
				<th>注意事项：</th>
				<td>下载远程图片，将占用一定的服务器资源，请避免在白天流量高峰期时段使用</td>
			<tr class="btnbox">
            	<th>&nbsp;</th>
				<td colspan="2">
				<input type="button" class="btn" value="下载所有站点图片" onClick="window.location.href='webpic.php?act=down&type=all';">&nbsp;
				<input type="button" class="btn" value="下载失败图片" onClick="window.location.href='webpic.php?act=down&type=part';">&nbsp;
                <input type="button" class="btn" value="失效图片检测" onClick="window.location.href='webpic.php?act=check';">
				</td>
			</tr>
		</table>
		</form>
	</div>
    <?php }?>

<?php echo '<script'; ?>
>
// 网站名称长度检查函数
function checkWebNameLength(input) {
    const value = input.value;
    const msgElement = document.getElementById('web_name_msg');

    // 计算字符长度（中文算2个字符）
    let length = 0;
    for (let i = 0; i < value.length; i++) {
        if (value.charCodeAt(i) > 127) {
            length += 2; // 中文字符算2个字符
        } else {
            length += 1; // 英文字符算1个字符
        }
    }

    if (length > 12) {
        msgElement.innerHTML = '<span style="color: #f00;">网站名称过长！最多12个字符（6个汉字）</span>';
        msgElement.style.color = '#f00';
        return false;
    } else if (length === 0) {
        msgElement.innerHTML = '最多12个字符（6个汉字）';
        msgElement.style.color = '#999';
    } else {
        msgElement.innerHTML = `已输入${length}/12个字符`;
        msgElement.style.color = '#666';
    }
    return true;
}

// 网站名称失焦验证
function checkWebName(name) {
    const msgElement = document.getElementById('web_name_msg');

    if (!name.trim()) {
        msgElement.innerHTML = '<span style="color: #f00;">请输入网站名称！</span>';
        return false;
    }

    // 计算字符长度
    let length = 0;
    for (let i = 0; i < name.length; i++) {
        if (name.charCodeAt(i) > 127) {
            length += 2;
        } else {
            length += 1;
        }
    }

    if (length > 12) {
        msgElement.innerHTML = '<span style="color: #f00;">网站名称过长！最多12个字符（6个汉字）</span>';
        return false;
    }

    msgElement.innerHTML = `已输入${length}/12个字符`;
    msgElement.style.color = '#666';
    return true;
}

// 安全的URL检测函数包装器
function safeCheckAdminUrl(url) {
    try {
        return checkAdminUrl(url);
    } catch (error) {
        console.error('URL检测出错:', error);
        const msgElement = document.getElementById('url_msg');
        if (msgElement) {
            msgElement.innerHTML = '<span style="color: #f00;">检测功能出错，请刷新页面重试</span>';
        }
        return false;
    }
}

// 后台URL检测函数
function checkAdminUrl(url) {
    const msgElement = document.getElementById('url_msg');

    // 确保消息元素存在
    if (!msgElement) {
        console.error('找不到url_msg元素');
        return false;
    }

    if (!url || url.trim() === '') {
        msgElement.innerHTML = '<span style="color: #999;">请输入完整的域名，不包含http://</span>';
        return false;
    }

    url = url.trim();

    // 简单的域名格式验证
    const domainPattern = /^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/;
    if (!domainPattern.test(url)) {
        msgElement.innerHTML = '<span style="color: #f00;">域名格式不正确！</span>';
        return false;
    }

    // 显示检测中状态
    msgElement.innerHTML = '<span style="color: #666;">🔍 正在检测...</span>';

    // 延迟执行AJAX请求，避免阻塞UI
    setTimeout(function() {
        performUrlCheck(url, msgElement);
    }, 100);

    return true;
}

// 执行URL检测的函数
function performUrlCheck(url, msgElement) {
    // 使用jQuery发送AJAX请求
    if (typeof $ !== 'undefined') {
        $.ajax({
            type: "GET",
            url: 'ajax_check.php',
            data: {
                type: 'check',
                url: url
            },
            cache: false,
            timeout: 8000,
            beforeSend: function() {
                msgElement.innerHTML = '<span style="color: #666;">🔍 正在检测...</span>';
            },
            success: function(data) {
                if (data && data.trim()) {
                    msgElement.innerHTML = data;
                } else {
                    msgElement.innerHTML = '<span style="color: #f00;">检测响应为空</span>';
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', xhr.status, status, error);
                let errorMsg = '检测失败';
                if (status === 'timeout') {
                    errorMsg = '检测超时';
                } else if (xhr.status === 404) {
                    errorMsg = '检测接口不存在';
                } else if (xhr.status === 500) {
                    errorMsg = '服务器内部错误';
                }
                msgElement.innerHTML = '<span style="color: #f00;">' + errorMsg + '</span>';
            }
        });
    } else {
        // 备用方案：原生JavaScript
        msgElement.innerHTML = '<span style="color: #f00;">jQuery未加载，无法进行检测</span>';
    }
}

// 表单提交验证
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form[name="mform"]');
    if (form && (form.action.includes('saveadd') || form.action.includes('saveedit'))) {
        form.addEventListener('submit', function(e) {
            const webNameInput = document.getElementById('web_name');
            if (webNameInput && !checkWebName(webNameInput.value)) {
                e.preventDefault();
                alert('网站名称不符合要求，请检查后重新提交！');
                webNameInput.focus();
                return false;
            }
        });
    }
});

// 页面加载完成后的初始化
$(document).ready(function() {
    // 检查必要的元素是否存在
    const urlInput = document.getElementById('web_url');
    const urlMsg = document.getElementById('url_msg');

    if (urlInput && urlMsg) {
        console.log('URL检测功能已就绪');

        // 如果输入框已有值，显示提示
        if (urlInput.value && urlInput.value.trim()) {
            urlMsg.innerHTML = '<span style="color: #999;">可以修改域名后重新检测</span>';
        }
    } else {
        console.warn('URL检测元素未找到');
    }

    // 全局错误处理
    window.onerror = function(msg, url, lineNo, columnNo, error) {
        console.error('JavaScript错误:', msg, 'at', url, ':', lineNo);
        return false; // 不阻止默认错误处理
    };
});
<?php echo '</script'; ?>
>

<?php $_smarty_tpl->_subTemplateRender("file:footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
}
}
