<?php
// 测试音乐播放器功能
define('IN_IWEBDIR', true);
define('APP_PATH', './source/');

require('./source/init.php');
require('./source/module/article.php');

echo "<h2>音乐播放器功能测试</h2>\n";

// 1. 检查分类318是否存在
echo "<h3>1. 检查分类318</h3>\n";
$cate_sql = "SELECT * FROM ".$DB->table('categories')." WHERE cate_id=318";
$cate_result = $DB->query($cate_sql);
if ($cate_row = $DB->fetch_array($cate_result)) {
    echo "✓ 分类318存在: " . $cate_row['cate_name'] . "<br>\n";
} else {
    echo "✗ 分类318不存在<br>\n";
}

// 2. 检查分类318下的文章数量
echo "<h3>2. 检查分类318下的文章</h3>\n";
$count_sql = "SELECT COUNT(*) as total FROM ".$DB->table('articles')." WHERE cate_id=318";
$count_result = $DB->query($count_sql);
$count_row = $DB->fetch_array($count_result);
echo "分类318下总文章数: " . $count_row['total'] . "<br>\n";

// 3. 检查包含MP3的文章
$mp3_sql = "SELECT COUNT(*) as total FROM ".$DB->table('articles')." WHERE cate_id=318 AND art_content LIKE '%.mp3%'";
$mp3_result = $DB->query($mp3_sql);
$mp3_row = $DB->fetch_array($mp3_result);
echo "包含MP3链接的文章数: " . $mp3_row['total'] . "<br>\n";

// 4. 检查已审核的文章
$approved_sql = "SELECT COUNT(*) as total FROM ".$DB->table('articles')." WHERE cate_id=318 AND art_content LIKE '%.mp3%' AND art_status=3";
$approved_result = $DB->query($approved_sql);
$approved_row = $DB->fetch_array($approved_result);
echo "已审核且包含MP3的文章数: " . $approved_row['total'] . "<br>\n";

// 5. 显示前几篇文章的内容
echo "<h3>3. 文章内容示例</h3>\n";
$sample_sql = "SELECT art_id, art_title, art_content, art_status FROM ".$DB->table('articles')." WHERE cate_id=318 LIMIT 3";
$sample_result = $DB->query($sample_sql);
$found_articles = false;
while ($sample_row = $DB->fetch_array($sample_result)) {
    $found_articles = true;
    echo "<h4>文章ID: {$sample_row['art_id']}, 标题: {$sample_row['art_title']}, 状态: {$sample_row['art_status']}</h4>\n";
    echo "<div style='border:1px solid #ccc; padding:10px; margin:10px 0; max-height:200px; overflow:auto;'>\n";
    echo htmlspecialchars(substr($sample_row['art_content'], 0, 1000)) . "...\n";
    echo "</div>\n";
    
    // 测试提取MP3链接
    $links = extract_music_urls($sample_row['art_content']);
    if (!empty($links)) {
        echo "✓ 提取到的MP3链接:<br>\n";
        foreach ($links as $link) {
            echo "- " . htmlspecialchars($link) . "<br>\n";
        }
    } else {
        echo "✗ 未提取到MP3链接<br>\n";
    }
    echo "<hr>\n";
}

if (!$found_articles) {
    echo "没有找到分类318的文章<br>\n";
}

// 6. 测试get_music_links函数
echo "<h3>4. 测试get_music_links函数</h3>\n";
$music_links = get_music_links(318, 10);
echo "get_music_links返回的结果数量: " . count($music_links) . "<br>\n";
if (!empty($music_links)) {
    echo "<ul>\n";
    foreach ($music_links as $link) {
        echo "<li>" . htmlspecialchars($link['title']) . " - " . htmlspecialchars($link['url']) . "</li>\n";
    }
    echo "</ul>\n";
} else {
    echo "✗ 没有返回任何音乐链接<br>\n";
}

// 7. 检查其他音乐分类
echo "<h3>5. 检查其他音乐相关分类</h3>\n";
$music_cates_sql = "SELECT cate_id, cate_name FROM ".$DB->table('categories')." WHERE cate_name LIKE '%音乐%' OR cate_name LIKE '%歌曲%'";
$music_cates_result = $DB->query($music_cates_sql);
while ($cate_row = $DB->fetch_array($music_cates_result)) {
    echo "分类ID: {$cate_row['cate_id']}, 名称: {$cate_row['cate_name']}<br>\n";
    
    // 检查这个分类下是否有文章
    $count_sql = "SELECT COUNT(*) as total FROM ".$DB->table('articles')." WHERE cate_id={$cate_row['cate_id']}";
    $count_result = $DB->query($count_sql);
    $count_row = $DB->fetch_array($count_result);
    echo "  - 文章数量: {$count_row['total']}<br>\n";
}

echo "<h3>6. 建议解决方案</h3>\n";
if ($approved_row['total'] == 0) {
    echo "<p style='color: red;'>问题：分类318下没有已审核且包含MP3链接的文章</p>\n";
    echo "<p>解决方案：</p>\n";
    echo "<ol>\n";
    echo "<li>检查是否有文章被误分类到其他分类</li>\n";
    echo "<li>检查文章审核状态，可能需要审核通过</li>\n";
    echo "<li>检查文章内容中MP3链接的格式是否正确</li>\n";
    echo "<li>考虑添加一些测试文章到分类318</li>\n";
    echo "</ol>\n";
}
?>
