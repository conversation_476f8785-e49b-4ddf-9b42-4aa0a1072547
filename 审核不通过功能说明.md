# 审核不通过功能说明

## 功能概述
为网站分类目录系统添加了审核不通过功能，在首页显示审核不通过的网站列表，包含网站名称和不通过原因。

## 主要修改

### 1. 数据库修改
- 在 `dir_websites` 表中添加了 `web_reject_reason` 字段
- 字段类型：`varchar(255) NOT NULL DEFAULT ''`
- 字段注释：审核不通过原因

### 2. 后端功能
- 在 `source/module/website.php` 中添加了 `get_rejected_websites()` 函数
- 修改了 `get_one_website()` 函数以支持审核不通过原因字段
- 在 `system/website.php` 中添加了对审核不通过原因的处理逻辑

### 3. 前端显示
- 在首页模板 `themes/default/index.html` 中添加了"审核不通过"容器
- 显示位置：在黑名单容器之后
- 显示内容：网站名称、提交时间、不通过原因
- 样式：使用橙色图标和文字标识

### 4. 分类页面
- 创建了审核不通过分类页面模块 `module/rejected.php`
- 创建了对应的模板文件 `themes/default/rejected.html`
- 支持分类筛选和分页功能
- 集成到网站导航菜单中

### 5. 详情页面
- 创建了专门的审核不通过详情页面模块 `module/rejected_detail.php`
- 创建了对应的模板文件 `themes/default/rejected_detail.html`
- 详情页面显示：网站基本信息、审核不通过原因、相关网站等
- 隐私保护：隐藏网站真实URL，不提供访问入口

### 6. 导航集成
- 在网站主导航菜单中添加了"审核不通过"链接
- 在统计信息中显示审核不通过网站数量
- 与待审核、黑名单等功能保持一致的设计风格

### 7. 后台管理
- 后台网站编辑页面已支持设置审核不通过原因
- 当网站状态设置为"审核不通过"时，可以填写具体原因

## 使用方法

### 管理员操作
1. 进入后台网站管理页面
2. 编辑需要设置为审核不通过的网站
3. 将网站状态设置为"审核不通过"
4. 在"审核不通过原因"字段中填写具体原因
5. 保存修改

### 前台显示
- 首页会自动显示最新的14个审核不通过网站
- 显示格式：网站名称 - 审核不通过（原因）
- 当天提交的网站会显示"new"标识
- 点击网站名称可以查看详细的审核不通过信息

### 详情页面功能
- 专门的审核不通过详情页面，URL格式：`?mod=rejected_detail&id=网站ID`
- 显示审核不通过的具体原因和处理建议
- 隐私保护：不显示真实网址，不提供访问入口
- 相关推荐：显示同分类的其他审核不通过网站
- 导航功能：支持上一站/下一站浏览

## 技术特点
- 兼容性：代码会自动检查数据库字段是否存在，确保向后兼容
- 安全性：所有输入都经过了适当的过滤和转义
- 隐私保护：自动过滤网站简介中的域名、邮箱、IP等敏感信息
- 用户体验：审核不通过的网站有明确的视觉标识和原因说明

## 数据库升级
如果是现有系统，需要执行以下SQL语句添加字段：

```sql
ALTER TABLE `dir_websites` 
ADD COLUMN `web_reject_reason` varchar(255) NOT NULL DEFAULT '' COMMENT '审核不通过原因';
```

## 注意事项
- 审核不通过原因字段为可选，如果不填写则不显示原因
- 系统会自动检查字段是否存在，确保在没有升级数据库的情况下也能正常运行
- 建议定期清理长期审核不通过的网站数据
