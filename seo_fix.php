<?php
/**
 * SEO自动修复脚本
 * 自动修复一些常见的SEO问题
 */

// 引入配置文件
require_once 'config.php';

class SEOFixer {
    private $db;
    private $fixed = [];
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * 执行所有SEO修复
     */
    public function runAllFixes() {
        echo "<h1>95目录网 SEO自动修复工具</h1>\n";
        echo "<p>修复时间：" . date('Y-m-d H:i:s') . "</p>\n";
        
        $this->fixMissingDescriptions();
        $this->fixMissingTags();
        $this->fixImageAltTags();
        $this->optimizeTitles();
        $this->updateSitemap();
        
        $this->generateFixReport();
    }
    
    /**
     * 修复缺失的网站描述
     */
    private function fixMissingDescriptions() {
        echo "<h2>1. 修复缺失的网站描述</h2>\n";
        
        $sql = "SELECT web_id, web_name, web_url, cate_id FROM " . $this->db->table('websites') . " 
                WHERE web_status = 3 AND (web_intro = '' OR web_intro IS NULL) LIMIT 50";
        $websites = $this->db->fetch_all($sql);
        
        $count = 0;
        foreach ($websites as $web) {
            // 获取分类信息
            $cate = $this->db->fetch_one("SELECT cate_name FROM " . $this->db->table('categories') . " WHERE cate_id = " . $web['cate_id']);
            $cate_name = $cate ? $cate['cate_name'] : '网站';
            
            // 生成描述
            $description = $web['web_name'] . "是一个优质的" . $cate_name . "网站，为用户提供专业的" . $cate_name . "服务。收录于95目录网" . $cate_name . "分类目录。";
            
            // 更新数据库
            $update_sql = "UPDATE " . $this->db->table('websites') . " 
                          SET web_intro = '" . $this->db->escape($description) . "' 
                          WHERE web_id = " . $web['web_id'];
            
            if ($this->db->query($update_sql)) {
                $count++;
            }
        }
        
        if ($count > 0) {
            echo "<p style='color: green;'>✅ 已为 {$count} 个网站生成描述</p>\n";
            $this->fixed[] = "为{$count}个网站生成描述";
        } else {
            echo "<p style='color: blue;'>ℹ️ 无需修复网站描述</p>\n";
        }
    }
    
    /**
     * 修复缺失的网站标签
     */
    private function fixMissingTags() {
        echo "<h2>2. 修复缺失的网站标签</h2>\n";
        
        $sql = "SELECT w.web_id, w.web_name, w.web_url, w.cate_id, c.cate_name 
                FROM " . $this->db->table('websites') . " w 
                LEFT JOIN " . $this->db->table('categories') . " c ON w.cate_id = c.cate_id 
                WHERE w.web_status = 3 AND (w.web_tags = '' OR w.web_tags IS NULL) LIMIT 50";
        $websites = $this->db->fetch_all($sql);
        
        $count = 0;
        foreach ($websites as $web) {
            // 生成标签
            $tags = [];
            
            // 从网站名称提取关键词
            $name_words = preg_split('/[\s\-_\.]+/', $web['web_name']);
            foreach ($name_words as $word) {
                if (mb_strlen($word, 'UTF-8') >= 2 && mb_strlen($word, 'UTF-8') <= 10) {
                    $tags[] = $word;
                }
            }
            
            // 添加分类相关标签
            if ($web['cate_name']) {
                $tags[] = $web['cate_name'];
                $tags[] = $web['cate_name'] . '网站';
            }
            
            // 添加通用标签
            $tags[] = '优质网站';
            $tags[] = '网站推荐';
            
            // 去重并限制数量
            $tags = array_unique($tags);
            $tags = array_slice($tags, 0, 8);
            $tags_str = implode(',', $tags);
            
            // 更新数据库
            $update_sql = "UPDATE " . $this->db->table('websites') . " 
                          SET web_tags = '" . $this->db->escape($tags_str) . "' 
                          WHERE web_id = " . $web['web_id'];
            
            if ($this->db->query($update_sql)) {
                $count++;
            }
        }
        
        if ($count > 0) {
            echo "<p style='color: green;'>✅ 已为 {$count} 个网站生成标签</p>\n";
            $this->fixed[] = "为{$count}个网站生成标签";
        } else {
            echo "<p style='color: blue;'>ℹ️ 无需修复网站标签</p>\n";
        }
    }
    
    /**
     * 修复图片Alt标签
     */
    private function fixImageAltTags() {
        echo "<h2>3. 修复缺失的网站图片</h2>\n";
        
        $sql = "SELECT web_id, web_name, web_url FROM " . $this->db->table('websites') . " 
                WHERE web_status = 3 AND (web_pic = '' OR web_pic IS NULL) LIMIT 30";
        $websites = $this->db->fetch_all($sql);
        
        $count = 0;
        foreach ($websites as $web) {
            // 生成默认图片URL（使用网站截图服务）
            $domain = parse_url('http://' . $web['web_url'], PHP_URL_HOST);
            if ($domain) {
                $pic_url = "https://cdn.iocdn.cc/mshots/v1/" . $domain;
                
                // 更新数据库
                $update_sql = "UPDATE " . $this->db->table('websites') . " 
                              SET web_pic = '" . $this->db->escape($pic_url) . "' 
                              WHERE web_id = " . $web['web_id'];
                
                if ($this->db->query($update_sql)) {
                    $count++;
                }
            }
        }
        
        if ($count > 0) {
            echo "<p style='color: green;'>✅ 已为 {$count} 个网站设置默认图片</p>\n";
            $this->fixed[] = "为{$count}个网站设置默认图片";
        } else {
            echo "<p style='color: blue;'>ℹ️ 无需修复网站图片</p>\n";
        }
    }
    
    /**
     * 优化网站标题
     */
    private function optimizeTitles() {
        echo "<h2>4. 优化网站标题</h2>\n";
        
        // 修复过短的标题
        $sql = "SELECT web_id, web_name, cate_id FROM " . $this->db->table('websites') . " 
                WHERE web_status = 3 AND CHAR_LENGTH(web_name) < 5 LIMIT 20";
        $shortTitles = $this->db->fetch_all($sql);
        
        $count = 0;
        foreach ($shortTitles as $web) {
            // 获取分类信息
            $cate = $this->db->fetch_one("SELECT cate_name FROM " . $this->db->table('categories') . " WHERE cate_id = " . $web['cate_id']);
            $cate_name = $cate ? $cate['cate_name'] : '';
            
            // 优化标题
            $new_title = $web['web_name'];
            if ($cate_name && strpos($web['web_name'], $cate_name) === false) {
                $new_title = $web['web_name'] . ' - ' . $cate_name . '网站';
            }
            
            if (mb_strlen($new_title, 'UTF-8') >= 5) {
                $update_sql = "UPDATE " . $this->db->table('websites') . " 
                              SET web_name = '" . $this->db->escape($new_title) . "' 
                              WHERE web_id = " . $web['web_id'];
                
                if ($this->db->query($update_sql)) {
                    $count++;
                }
            }
        }
        
        if ($count > 0) {
            echo "<p style='color: green;'>✅ 已优化 {$count} 个网站标题</p>\n";
            $this->fixed[] = "优化{$count}个网站标题";
        } else {
            echo "<p style='color: blue;'>ℹ️ 无需优化网站标题</p>\n";
        }
    }
    
    /**
     * 更新Sitemap
     */
    private function updateSitemap() {
        echo "<h2>5. 更新Sitemap文件</h2>\n";
        
        // 检查sitemap模块是否存在
        if (file_exists('module/sitemap.php')) {
            // 触发sitemap更新
            $sitemap_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/?mod=sitemap&format=xml';
            
            // 使用curl或file_get_contents触发sitemap生成
            if (function_exists('curl_init')) {
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $sitemap_url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                $result = curl_exec($ch);
                curl_close($ch);
                
                if ($result) {
                    echo "<p style='color: green;'>✅ Sitemap已更新</p>\n";
                    $this->fixed[] = "更新Sitemap文件";
                } else {
                    echo "<p style='color: orange;'>⚠️ Sitemap更新可能失败</p>\n";
                }
            } else {
                echo "<p style='color: blue;'>ℹ️ 请手动访问 ?mod=sitemap 更新Sitemap</p>\n";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ Sitemap模块不存在</p>\n";
        }
    }
    
    /**
     * 生成修复报告
     */
    private function generateFixReport() {
        echo "<h2>SEO修复总结</h2>\n";
        
        if (empty($this->fixed)) {
            echo "<p style='color: blue; font-size: 18px;'>ℹ️ 未发现需要修复的问题</p>\n";
        } else {
            echo "<p style='color: green; font-size: 18px;'>✅ 已完成 " . count($this->fixed) . " 项修复：</p>\n";
            echo "<ol>\n";
            foreach ($this->fixed as $fix) {
                echo "<li>{$fix}</li>\n";
            }
            echo "</ol>\n";
        }
        
        echo "<h3>后续建议</h3>\n";
        echo "<ul>\n";
        echo "<li>定期运行SEO检查工具</li>\n";
        echo "<li>监控网站性能和加载速度</li>\n";
        echo "<li>定期更新网站内容</li>\n";
        echo "<li>关注搜索引擎算法更新</li>\n";
        echo "</ul>\n";
    }
}

// 执行SEO修复
if (isset($_GET['fix']) && $_GET['fix'] == 'seo') {
    $fixer = new SEOFixer($DB);
    $fixer->runAllFixes();
} else {
    echo "<h1>SEO自动修复工具</h1>\n";
    echo "<p><a href='?fix=seo'>点击开始SEO修复</a></p>\n";
    echo "<p style='color: red;'>注意：修复操作会修改数据库，请先备份数据！</p>\n";
}
?>
