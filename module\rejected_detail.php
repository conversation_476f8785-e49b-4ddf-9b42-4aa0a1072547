<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$pagename = '审核不通过网站详情';
$pageurl = '?mod=rejected_detail';
$tempfile = 'rejected_detail.html';

$web_id = intval($_GET['id']);
$cache_id = $web_id;

if (!$web_id) {
    redirect('?mod=index');
}

if (!$smarty->isCached($tempfile, $cache_id)) {
	// 检查web_reject_reason字段是否存在
	$table_name = $DB->table('websites');
	$check_sql = "SHOW COLUMNS FROM `{$table_name}` LIKE 'web_reject_reason'";
	$check_result = $DB->query($check_sql);
	$has_reject_reason_field = $DB->num_rows($check_result) > 0;

	// 查询审核不通过网站，关联webdata表获取更新时间
	if ($has_reject_reason_field) {
		$sql = "SELECT w.web_id, w.web_name, w.web_url, w.web_intro, w.web_ai_intro, w.web_tags, w.web_ctime, w.cate_id, w.user_id, w.web_pic,
		               w.web_reject_reason, d.web_utime
		        FROM " . $DB->table('websites') . " w
		        LEFT JOIN " . $DB->table('webdata') . " d ON w.web_id = d.web_id
		        WHERE w.web_status = 4 AND w.web_id = $web_id
		        LIMIT 1";
	} else {
		$sql = "SELECT w.web_id, w.web_name, w.web_url, w.web_intro, w.web_ai_intro, w.web_tags, w.web_ctime, w.cate_id, w.user_id, w.web_pic,
		               d.web_utime
		        FROM " . $DB->table('websites') . " w
		        LEFT JOIN " . $DB->table('webdata') . " d ON w.web_id = d.web_id
		        WHERE w.web_status = 4 AND w.web_id = $web_id
		        LIMIT 1";
	}

	$web = $DB->fetch_one($sql);
	if (!$web) {
		redirect('?mod=index');
	}

	// 如果字段不存在，设置默认值
	if (!$has_reject_reason_field) {
		$web['web_reject_reason'] = '';
	}

	$cate = get_one_category($web['cate_id']);
	$user = get_one_user($web['user_id']);

	$smarty->assign('site_title', $web['web_name'].' - '.$pagename.' - '.$options['site_name']);
	$smarty->assign('site_keywords', !empty($web['web_tags']) ? $web['web_tags'] : $options['site_keywords']);
	$smarty->assign('site_description', !empty($web['web_intro']) ? mb_substr(strip_tags($web['web_intro']), 0, 200, 'utf-8') : $options['site_description']);
	$smarty->assign('site_path', get_sitepath().' &raquo; <a href="?mod=index">首页</a> &raquo; <a href="?mod=webdir&cid='.$web['cate_id'].'">'.$cate['cate_name'].'</a> &raquo; '.$web['web_name'].' - 审核不通过');
	$smarty->assign('site_rss', get_rssfeed());

	// 处理审核不通过数据
	if (empty($web['web_reject_reason'])) {
		$web['web_reject_reason'] = '该网站未通过审核';
	}

	$web['ctime_formatted'] = date('Y-m-d', $web['web_ctime']);

	// 隐藏网站URL，只显示域名
	$web['domain'] = parse_url('http://' . $web['web_url'], PHP_URL_HOST);
	if (!$web['domain']) {
		$web['domain'] = $web['web_url'];
	}

	// 隐私保护：网址信息设为隐藏状态
	$web['web_url_display'] = '隐私保护';
	$web['web_furl'] = '隐私保护';

	$web['web_pic'] = get_webthumb($web['web_pic']);
	$web['web_ip'] = '隐私保护'; // 隐藏IP信息
	$web['web_ctime'] = date('Y-m-d', $web['web_ctime']);
	$web['web_utime'] = $web['web_utime'] ? date('Y-m-d', $web['web_utime']) : date('Y-m-d', $web['web_ctime']);

	// 过滤网站简介和AI简介中的域名信息
	if (!empty($web['web_intro'])) {
		$web['web_intro'] = filter_domains_from_text($web['web_intro']);
	}
	if (!empty($web['web_ai_intro'])) {
		$web['web_ai_intro'] = filter_domains_from_text($web['web_ai_intro']);
	}

	// PageRank处理 - 审核不通过状态显示默认值
	$web['web_prank'] = 0;
	$web['web_grank'] = 0;
	$web['web_brank'] = 0;
	$web['web_srank'] = 0;
	$web['web_arank'] = 0;
	$web['web_views'] = 0; // 审核不通过状态浏览量为0
	$web['web_instat'] = 0; // 入站次数为0
	$web['web_outstat'] = 0; // 出站次数为0

	$smarty->assign('cate_id', $cate['cate_id']);
	$smarty->assign('cate_name', $cate['cate_name']);
	$smarty->assign('cate_keywords', $cate['cate_keywords']);
	$smarty->assign('cate_description', $cate['cate_description']);

	/** tags */
	$web_tags = get_format_tags($web['web_tags']);
	$smarty->assign('web_tags', $web_tags);

	// 获取同分类的其他审核不通过网站
	$related_rejected = $DB->fetch_all("SELECT web_id, web_name, web_pic, web_url FROM ".$DB->table('websites')."
	                                  WHERE cate_id=".$web['cate_id']." AND web_status=4 AND web_id!=$web_id
	                                  ORDER BY web_ctime DESC LIMIT 8");

	// 处理相关审核不通过网站的图片和链接
	foreach ($related_rejected as &$rel) {
		$rel['web_pic'] = get_webthumb($rel['web_pic']);
		$rel['web_link'] = '?mod=rejected_detail&id='.$rel['web_id'];
	}

    $smarty->assign('web', $web);
	$smarty->assign('user', $user);
	$smarty->assign('related_rejected', $related_rejected);

	// 获取上一站下一站（同分类的审核不通过网站）
	$prev_rejected = $DB->fetch_one("SELECT web_id, web_name FROM ".$DB->table('websites')."
	                               WHERE cate_id=".$web['cate_id']." AND web_status=4 AND web_id<$web_id
	                               ORDER BY web_id DESC LIMIT 1");
	$next_rejected = $DB->fetch_one("SELECT web_id, web_name FROM ".$DB->table('websites')."
	                               WHERE cate_id=".$web['cate_id']." AND web_status=4 AND web_id>$web_id
	                               ORDER BY web_id ASC LIMIT 1");

	// 为上一站下一站添加链接
	if ($prev_rejected) {
		$prev_rejected['web_link'] = '?mod=rejected_detail&id='.$prev_rejected['web_id'];
	}
	if ($next_rejected) {
		$next_rejected['web_link'] = '?mod=rejected_detail&id='.$next_rejected['web_id'];
	}

	$smarty->assign('prev_website', $prev_rejected);
	$smarty->assign('next_website', $next_rejected);

	// 相关站点显示同分类的正常网站
	$smarty->assign('related_website', get_websites($web['cate_id'], 10, false, false, 'ctime'));
}

smarty_output($tempfile, $cache_id);
?>
