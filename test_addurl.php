<?php
// 简单的测试脚本来验证addurl模块
define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('MOD_PATH', ROOT_PATH.'module/');

try {
    require(APP_PATH.'init.php');
    require(APP_PATH.'module/category.php');
    
    echo "测试开始...\n";
    
    // 测试数据库连接
    if (isset($DB) && $DB) {
        echo "✓ 数据库连接成功\n";
    } else {
        echo "✗ 数据库连接失败\n";
        exit;
    }
    
    // 测试分类选项获取
    $category_option = get_category_option('webdir', 0);
    if (!empty($category_option)) {
        echo "✓ 分类选项获取成功\n";
        echo "分类选项预览: " . substr($category_option, 0, 100) . "...\n";
    } else {
        echo "✗ 分类选项获取失败\n";
    }
    
    // 测试配置选项
    if (isset($options) && is_array($options)) {
        echo "✓ 配置选项加载成功\n";
        echo "网站名称: " . $options['site_name'] . "\n";
        echo "提交功能状态: " . (isset($options['is_enabled_submit']) ? $options['is_enabled_submit'] : '未设置') . "\n";
    } else {
        echo "✗ 配置选项加载失败\n";
    }
    
    // 测试模板系统
    if (isset($smarty) && $smarty) {
        echo "✓ 模板系统初始化成功\n";
    } else {
        echo "✗ 模板系统初始化失败\n";
    }
    
    echo "\n测试完成！\n";
    echo "如果所有项目都显示 ✓，说明addurl模块应该能正常工作。\n";
    
} catch (Exception $e) {
    echo "✗ 测试过程中发生错误: " . $e->getMessage() . "\n";
}
?>
