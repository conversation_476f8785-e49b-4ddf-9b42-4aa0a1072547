<?php
// 测试简化版标题提取
define('IN_IWEBDIR', true);
define('APP_PATH', './source/');

require('./source/init.php');
require('./source/module/article.php');

echo "<h2>测试简化版标题提取</h2>\n";

// 测试你的JSON格式
$json_content = '{
    "id": 36685,
    "desc": "20分钟试听经典国语dj串烧，劲爆dj合集，DJ打碟现场",
    "music": "https://sf5-hl-cdn-tos.douyinstatic.com/obj/ies-music/7499955641297996554.mp3"
}';

echo "<h3>1. 测试JSON内容</h3>\n";
echo "<h4>原始内容:</h4>\n";
echo "<pre>" . htmlspecialchars($json_content) . "</pre>\n";

// 测试中文提取
$chinese_text = extract_chinese_text($json_content);
echo "<h4>提取的中文:</h4>\n";
echo "<p>'" . htmlspecialchars($chinese_text) . "'</p>\n";

// 测试完整提取
$music_data = extract_music_urls_with_titles($json_content);
echo "<h4>完整提取结果:</h4>\n";
if (!empty($music_data)) {
    foreach ($music_data as $i => $data) {
        echo "<p><strong>音乐 " . ($i + 1) . ":</strong><br>";
        echo "标题: '" . htmlspecialchars($data['title']) . "'<br>";
        echo "URL: " . htmlspecialchars($data['url']) . "</p>\n";
    }
} else {
    echo "<p style='color: red;'>没有提取到数据</p>\n";
}

// 测试其他格式
echo "<h3>2. 测试其他格式</h3>\n";

$test_cases = array(
    '周杰伦青花瓷经典歌曲 https://music.163.com/song/media/outer/url?id=185668',
    'https://example.com/music/unknown.mp3',
    '这是一首好听的歌曲，邓紫棋泡沫 https://example.com/paomo.mp3'
);

foreach ($test_cases as $i => $case) {
    echo "<h4>测试案例 " . ($i + 1) . ":</h4>\n";
    echo "<p>内容: " . htmlspecialchars($case) . "</p>\n";
    
    $chinese = extract_chinese_text($case);
    echo "<p>提取中文: '" . htmlspecialchars($chinese) . "'</p>\n";
    
    $result = extract_music_urls_with_titles($case);
    if (!empty($result)) {
        foreach ($result as $data) {
            echo "<p>最终标题: '" . htmlspecialchars($data['title']) . "'</p>\n";
        }
    }
    echo "<br>\n";
}

// 创建测试文章
echo "<h3>3. 创建测试文章</h3>\n";
$DB->query("DELETE FROM ".$DB->table('articles')." WHERE art_title='简化标题测试'");

$title = $DB->escape_string('简化标题测试');
$content = $DB->escape_string($json_content);

$sql = "INSERT INTO ".$DB->table('articles')." 
        (user_id, cate_id, art_title, art_tags, art_intro, art_content, art_status, art_ctime, art_utime) 
        VALUES 
        (1, 319, '$title', '测试', '测试', '$content', 3, ".time().", ".time().")";

if ($DB->query($sql)) {
    $art_id = $DB->insert_id();
    echo "<p style='color: green;'>测试文章创建成功 (ID: $art_id)</p>\n";
    
    // 测试get_music_links
    $music_links = get_music_links(319, 20);
    foreach ($music_links as $link) {
        if ($link['art_id'] == $art_id) {
            echo "<h4>get_music_links结果:</h4>\n";
            echo "<p><strong>显示标题:</strong> " . htmlspecialchars($link['title']) . "</p>\n";
            echo "<p><strong>提取标题:</strong> " . htmlspecialchars($link['extracted_title'] ?? '无') . "</p>\n";
            echo "<p><strong>文章标题:</strong> " . htmlspecialchars($link['original_title']) . "</p>\n";
            break;
        }
    }
} else {
    echo "<p style='color: red;'>创建文章失败</p>\n";
}

echo "<h3>4. 预期结果</h3>\n";
echo "<p>现在应该显示: <strong>20分钟试听经典国语dj串烧劲爆dj合集DJ打碟现场 [DJ串烧]</strong></p>\n";
echo "<p>而不是: 简化标题测试 [DJ串烧]</p>\n";

echo "<h3>5. 测试AJAX接口</h3>\n";
echo "<p><a href='?mod=ajaxget&type=music_list' target='_blank'>点击查看AJAX接口返回的数据</a></p>\n";
?>
