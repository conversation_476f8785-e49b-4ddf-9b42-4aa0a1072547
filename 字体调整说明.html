<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速提交页面字体调整说明</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            line-height: 1.6;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-link { 
            display: inline-block; 
            padding: 12px 24px; 
            background: #007bff; 
            color: white; 
            text-decoration: none; 
            border-radius: 5px; 
            margin: 10px 5px;
            font-size: 14px;
        }
        .test-link:hover { 
            background: #0056b3; 
            color: white;
        }
        .info { 
            background: #f8f9fa; 
            border: 1px solid #dee2e6; 
            padding: 20px; 
            border-radius: 5px; 
            margin: 20px 0;
        }
        .changes {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .font-demo {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            background: #fff;
        }
        h1 { color: #333; text-align: center; }
        h2 { color: #007bff; border-bottom: 2px solid #007bff; padding-bottom: 5px; }
        h3 { color: #28a745; }
        code { 
            background: #f8f9fa; 
            padding: 2px 6px; 
            border-radius: 3px; 
            font-family: 'Courier New', monospace;
        }
        .size-13 { font-size: 13px; }
        .size-12 { font-size: 12px; }
        .size-14 { font-size: 14px; }
        .size-20 { font-size: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 快速提交页面字体调整说明</h1>
        
        <div class="info">
            <h3>📋 调整概述</h3>
            <p>为了使快速提交页面看起来更加协调，我们对各个元素的字体大小进行了精细调整，确保整体视觉效果的统一性和可读性。</p>
        </div>

        <h2>🔧 字体大小调整详情</h2>

        <div class="changes">
            <h3>主要调整项目：</h3>
            
            <div class="font-demo">
                <strong>1. 表单整体字体</strong>
                <div class="size-13">• 表单基础字体：13px（原来混合使用14px和默认）</div>
                <div class="size-13">• 行高：1.6（提升可读性）</div>
            </div>

            <div class="font-demo">
                <strong>2. 标题字体</strong>
                <div class="size-20">• 主标题：20px（原来24px，更协调）</div>
                <div class="size-14">• 副标题：14px（原来16px）</div>
            </div>

            <div class="font-demo">
                <strong>3. 表单标签</strong>
                <div class="size-13">• 字段标签：13px，加粗显示</div>
                <div class="size-13">• 输入框：13px，统一字体</div>
                <div class="size-13">• 按钮：13px，正常字重</div>
            </div>

            <div class="font-demo">
                <strong>4. 提示文字</strong>
                <div class="size-12">• 帮助提示：12px（如"不需要输入http://或www"）</div>
                <div class="size-12">• 字符计数：12px（如"已输入5/12个字符"）</div>
                <div class="size-12">• 错误提示：12px，红色显示</div>
            </div>

            <div class="font-demo">
                <strong>5. 内容区域</strong>
                <div class="size-13">• 提交须知：13px，行高1.8</div>
                <div class="size-13">• 优势说明：13px，行高1.6</div>
            </div>
        </div>

        <h2>📱 响应式调整</h2>
        <div class="info">
            <h3>移动端优化：</h3>
            <ul>
                <li><strong>表单字体</strong>：12px（桌面端13px）</li>
                <li><strong>标签字体</strong>：12px（桌面端13px）</li>
                <li><strong>按钮字体</strong>：12px，调整内边距</li>
                <li><strong>主标题</strong>：18px（桌面端20px）</li>
                <li><strong>副标题</strong>：13px（桌面端14px）</li>
            </ul>
        </div>

        <h2>🎯 设计原则</h2>
        <div class="info">
            <ol>
                <li><strong>层次分明</strong>：主标题 > 副标题 > 正文 > 提示文字</li>
                <li><strong>统一协调</strong>：相同类型的元素使用相同字体大小</li>
                <li><strong>可读性优先</strong>：确保在各种设备上都有良好的阅读体验</li>
                <li><strong>视觉平衡</strong>：字体大小与元素重要性相匹配</li>
            </ol>
        </div>

        <h2>🔍 字体大小对比</h2>
        <div class="font-demo">
            <div style="font-size: 20px; font-weight: bold; margin-bottom: 10px;">主标题示例 (20px)</div>
            <div style="font-size: 14px; margin-bottom: 10px;">副标题示例 (14px)</div>
            <div style="font-size: 13px; margin-bottom: 10px;">表单标签示例 (13px)</div>
            <div style="font-size: 13px; margin-bottom: 10px;">正文内容示例 (13px)</div>
            <div style="font-size: 12px; color: #666;">提示文字示例 (12px)</div>
        </div>

        <h2>🚀 测试链接</h2>
        <div style="text-align: center; margin: 30px 0;">
            <a href="https://www.95dir.com/?mod=quicksubmit" class="test-link" target="_blank">
                查看调整后的快速提交页面
            </a>
            <a href="https://www.95dir.com/?mod=addurl" class="test-link" target="_blank">
                对比原有提交页面
            </a>
        </div>

        <div class="changes">
            <h3>✅ 调整效果：</h3>
            <ul>
                <li>整体视觉更加统一协调</li>
                <li>文字层次更加分明</li>
                <li>移动端适配更加友好</li>
                <li>用户体验显著提升</li>
                <li>与网站整体风格保持一致</li>
            </ul>
        </div>

        <div class="info">
            <p><strong>💡 提示：</strong>所有字体调整都考虑了网站的整体设计风格，确保与其他页面保持一致的视觉体验。</p>
        </div>
    </div>
</body>
</html>
