<?php
// 快速测试音乐标题提取
define('IN_IWEBDIR', true);
define('APP_PATH', './source/');

require('./source/init.php');
require('./source/module/article.php');

echo "<h2>快速测试音乐标题提取</h2>\n";

// 简单测试内容
$test_content = '
周杰伦 - 青花瓷：https://music.163.com/song/media/outer/url?id=185668
邓紫棋《泡沫》 https://example.com/music/paomo.mp3
纯链接：https://example.com/music/unknown.mp3
';

echo "<h3>测试内容：</h3>\n";
echo "<pre>" . htmlspecialchars($test_content) . "</pre>\n";

echo "<h3>提取结果：</h3>\n";
$music_data = extract_music_urls_with_titles($test_content);

if (!empty($music_data)) {
    foreach ($music_data as $i => $data) {
        echo "<p>" . ($i + 1) . ". 标题: '" . htmlspecialchars($data['title']) . "' | URL: " . htmlspecialchars($data['url']) . "</p>\n";
    }
} else {
    echo "<p style='color: red;'>没有提取到数据</p>\n";
}

// 删除并重新创建测试文章
$DB->query("DELETE FROM ".$DB->table('articles')." WHERE art_title='快速标题测试'");

$sql = "INSERT INTO ".$DB->table('articles')." 
        (user_id, cate_id, art_title, art_tags, art_intro, art_content, art_status, art_ctime, art_utime) 
        VALUES 
        (1, 318, '快速标题测试', '测试', '测试', '" . $DB->escape_string($test_content) . "', 3, " . time() . ", " . time() . ")";

if ($DB->query($sql)) {
    echo "<h3>测试文章创建成功</h3>\n";
    
    // 测试get_music_links
    $links = get_music_links(318, 10);
    echo "<h3>get_music_links结果：</h3>\n";
    foreach ($links as $link) {
        if (strpos($link['title'], '快速标题测试') !== false || strpos($link['title'], '周杰伦') !== false || strpos($link['title'], '邓紫棋') !== false) {
            echo "<p><strong>" . htmlspecialchars($link['title']) . "</strong><br>";
            echo "URL: " . htmlspecialchars($link['url']) . "<br>";
            echo "提取标题: " . htmlspecialchars($link['extracted_title'] ?? '无') . "</p>\n";
        }
    }
} else {
    echo "<p style='color: red;'>创建文章失败</p>\n";
}
?>
