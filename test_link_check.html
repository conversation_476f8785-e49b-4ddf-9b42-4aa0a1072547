<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>链接检测测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-item { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .loading { background-color: #fff3cd; border-color: #ffeaa7; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>链接检测功能测试</h1>
    
    <div>
        <button onclick="testSingleUrl()">测试单个URL</button>
        <button onclick="testMultipleUrls()">测试多个URL</button>
        <button onclick="clearResults()">清除结果</button>
    </div>
    
    <div id="results"></div>

    <script>
        const CHECK_ENDPOINT = '/module/status_check.php?url=';
        
        // 测试URL列表
        const TEST_URLS = [
            'https://www.baidu.com',
            'https://www.google.com',
            'https://www.95dir.com/?mod=webdir',
            'https://www.95dir.com/?mod=siteinfo&wid=481',
            'https://httpstat.us/404',  // 测试404
            'https://httpstat.us/500'   // 测试500
        ];

        async function fetchStatus(url) {
            try {
                const res = await fetch(CHECK_ENDPOINT + encodeURIComponent(url));
                
                if (!res.ok) {
                    throw new Error(`HTTP ${res.status}: ${res.statusText}`);
                }
                
                const data = await res.json();
                
                if (data.error) {
                    console.error('状态检测API错误:', data.error, data.debug || '');
                    throw new Error(data.error);
                }
                
                const code = Number(data.status);
                if (!code) {
                    console.error('API返回数据异常:', data);
                    throw new Error('接口返回缺少 status 字段');
                }
                
                return { success: true, data: data };
            } catch (error) {
                console.error('链接状态检测失败:', {
                    url: url,
                    endpoint: CHECK_ENDPOINT + encodeURIComponent(url),
                    error: error.message
                });
                return { success: false, error: error.message };
            }
        }

        async function testSingleUrl() {
            const url = prompt('请输入要测试的URL:', 'https://www.baidu.com');
            if (!url) return;
            
            await testUrl(url);
        }

        async function testMultipleUrls() {
            for (const url of TEST_URLS) {
                await testUrl(url);
                // 添加延迟避免请求过于频繁
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }

        async function testUrl(url) {
            const resultsDiv = document.getElementById('results');
            const testDiv = document.createElement('div');
            testDiv.className = 'test-item loading';
            testDiv.innerHTML = `
                <h3>测试: ${url}</h3>
                <p>检测中...</p>
            `;
            resultsDiv.appendChild(testDiv);

            const startTime = Date.now();
            const result = await fetchStatus(url);
            const endTime = Date.now();
            const duration = endTime - startTime;

            if (result.success) {
                testDiv.className = 'test-item success';
                testDiv.innerHTML = `
                    <h3>✓ 测试成功: ${url}</h3>
                    <p><strong>状态码:</strong> ${result.data.status}</p>
                    <p><strong>服务器:</strong> ${result.data.server || 'Unknown'}</p>
                    <p><strong>响应时间:</strong> ${result.data.response_time}ms (客户端: ${duration}ms)</p>
                    <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            } else {
                testDiv.className = 'test-item error';
                testDiv.innerHTML = `
                    <h3>✗ 测试失败: ${url}</h3>
                    <p><strong>错误:</strong> ${result.error}</p>
                    <p><strong>耗时:</strong> ${duration}ms</p>
                `;
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // 页面加载完成后显示基本信息
        window.onload = function() {
            console.log('链接检测测试页面已加载');
            console.log('检测端点:', CHECK_ENDPOINT);
        };
    </script>
</body>
</html>
