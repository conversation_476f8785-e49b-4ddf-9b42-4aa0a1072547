<?php
// 测试每个链接对应的标题
define('IN_IWEBDIR', true);
define('APP_PATH', './source/');

require('./source/init.php');
require('./source/module/article.php');

echo "<h2>测试每个链接对应的标题</h2>\n";

// 测试多个音乐的内容
$test_content = '
这里有几首好听的歌：

周杰伦青花瓷经典歌曲：https://music.163.com/song/media/outer/url?id=185668

邓紫棋泡沫流行音乐 https://example.com/music/paomo.mp3

林俊杰江南怀旧歌曲：https://y.qq.com/n/yqq/song/001234567.html

纯链接（无标题）：https://example.com/music/unknown.mp3
';

echo "<h3>1. 测试多个音乐链接</h3>\n";
echo "<h4>原始内容:</h4>\n";
echo "<pre>" . htmlspecialchars($test_content) . "</pre>\n";

$music_data = extract_music_urls_with_titles($test_content);
echo "<h4>提取结果:</h4>\n";
if (!empty($music_data)) {
    foreach ($music_data as $i => $data) {
        echo "<p><strong>音乐 " . ($i + 1) . ":</strong><br>";
        echo "标题: '" . htmlspecialchars($data['title']) . "'<br>";
        echo "URL: " . htmlspecialchars($data['url']) . "</p>\n";
    }
} else {
    echo "<p style='color: red;'>没有提取到数据</p>\n";
}

// 测试JSON格式
$json_content = '{
    "id": 36685,
    "desc": "20分钟试听经典国语dj串烧劲爆dj合集DJ打碟现场",
    "music": "https://sf5-hl-cdn-tos.douyinstatic.com/obj/ies-music/7499955641297996554.mp3"
}';

echo "<h3>2. 测试JSON格式</h3>\n";
echo "<h4>JSON内容:</h4>\n";
echo "<pre>" . htmlspecialchars($json_content) . "</pre>\n";

$json_music_data = extract_music_urls_with_titles($json_content);
echo "<h4>提取结果:</h4>\n";
if (!empty($json_music_data)) {
    foreach ($json_music_data as $i => $data) {
        echo "<p><strong>音乐 " . ($i + 1) . ":</strong><br>";
        echo "标题: '" . htmlspecialchars($data['title']) . "'<br>";
        echo "URL: " . htmlspecialchars($data['url']) . "</p>\n";
    }
} else {
    echo "<p style='color: red;'>没有提取到数据</p>\n";
}

// 测试多个JSON对象
$multi_json = '[
    {
        "desc": "第一首歌经典老歌怀旧金曲",
        "music": "https://example.com/song1.mp3"
    },
    {
        "desc": "第二首歌流行音乐热门单曲",
        "music": "https://example.com/song2.mp3"
    }
]';

echo "<h3>3. 测试多个JSON对象</h3>\n";
echo "<h4>JSON数组:</h4>\n";
echo "<pre>" . htmlspecialchars($multi_json) . "</pre>\n";

$multi_music_data = extract_music_urls_with_titles($multi_json);
echo "<h4>提取结果:</h4>\n";
if (!empty($multi_music_data)) {
    foreach ($multi_music_data as $i => $data) {
        echo "<p><strong>音乐 " . ($i + 1) . ":</strong><br>";
        echo "标题: '" . htmlspecialchars($data['title']) . "'<br>";
        echo "URL: " . htmlspecialchars($data['url']) . "</p>\n";
    }
} else {
    echo "<p style='color: red;'>没有提取到数据</p>\n";
}

// 创建测试文章
echo "<h3>4. 创建测试文章</h3>\n";
$DB->query("DELETE FROM ".$DB->table('articles')." WHERE art_title='个别标题测试'");

$title = $DB->escape_string('个别标题测试');
$content = $DB->escape_string($test_content);

$sql = "INSERT INTO ".$DB->table('articles')." 
        (user_id, cate_id, art_title, art_tags, art_intro, art_content, art_status, art_ctime, art_utime) 
        VALUES 
        (1, 318, '$title', '测试', '测试', '$content', 3, ".time().", ".time().")";

if ($DB->query($sql)) {
    $art_id = $DB->insert_id();
    echo "<p style='color: green;'>测试文章创建成功 (ID: $art_id)</p>\n";
    
    // 测试get_music_links
    $music_links = get_music_links(318, 20);
    echo "<h4>get_music_links结果:</h4>\n";
    foreach ($music_links as $link) {
        if ($link['art_id'] == $art_id) {
            echo "<p><strong>显示标题:</strong> " . htmlspecialchars($link['title']) . "<br>";
            echo "<strong>提取标题:</strong> " . htmlspecialchars($link['extracted_title'] ?? '无') . "<br>";
            echo "<strong>URL:</strong> " . htmlspecialchars($link['url']) . "</p>\n";
            echo "<hr>\n";
        }
    }
} else {
    echo "<p style='color: red;'>创建文章失败</p>\n";
}

echo "<h3>5. 预期结果</h3>\n";
echo "<p>现在应该看到每个链接都有自己对应的标题：</p>\n";
echo "<ul>\n";
echo "<li>周杰伦青花瓷经典歌曲 1 [流行歌曲]</li>\n";
echo "<li>邓紫棋泡沫流行音乐 2 [流行歌曲]</li>\n";
echo "<li>林俊杰江南怀旧歌曲 3 [流行歌曲]</li>\n";
echo "<li>个别标题测试 4 [流行歌曲] (无标题的链接使用文章标题)</li>\n";
echo "</ul>\n";

echo "<h3>6. 测试AJAX接口</h3>\n";
echo "<p><a href='?mod=ajaxget&type=music_list' target='_blank'>点击查看AJAX接口返回的数据</a></p>\n";
?>
