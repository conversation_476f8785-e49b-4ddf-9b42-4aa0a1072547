<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速提交测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-link { 
            display: inline-block; 
            padding: 10px 20px; 
            background: #007bff; 
            color: white; 
            text-decoration: none; 
            border-radius: 5px; 
            margin: 10px;
        }
        .test-link:hover { background: #0056b3; }
        .info { 
            background: #f8f9fa; 
            border: 1px solid #dee2e6; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🚀 快速提交功能测试</h1>
    
    <div class="info">
        <h3>功能说明：</h3>
        <ul>
            <li><strong>原有addurl.php</strong> - 保持不变，会员和非会员都可以使用</li>
            <li><strong>新增quicksubmit.php</strong> - 专门为非会员设计的快速提交页面</li>
            <li>两个页面功能相似，但quicksubmit有更好的用户体验和界面设计</li>
        </ul>
    </div>
    
    <h3>测试链接：</h3>
    <a href="https://www.95dir.com/?mod=addurl" class="test-link" target="_blank">原有提交页面 (addurl)</a>
    <a href="https://www.95dir.com/?mod=quicksubmit" class="test-link" target="_blank">快速提交页面 (quicksubmit)</a>
    
    <div class="info">
        <h3>主要改进：</h3>
        <ul>
            <li>✅ 更现代化的界面设计</li>
            <li>✅ 更详细的表单验证</li>
            <li>✅ 实时字符计数</li>
            <li>✅ 更友好的错误提示</li>
            <li>✅ 响应式设计</li>
            <li>✅ 更清晰的提交须知</li>
        </ul>
    </div>
    
    <div class="info">
        <h3>使用建议：</h3>
        <p>可以将快速提交页面的链接放在网站首页或导航菜单中，作为主要的网站提交入口，为用户提供更好的体验。</p>
        <p>例如：<code>https://www.95dir.com/?mod=quicksubmit</code></p>
    </div>
</body>
</html>
