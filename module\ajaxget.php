<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

require(APP_PATH.'module/webdata.php');

$type = trim($_GET['type']);
/** check site */
if ($type == 'check') {
	$url = trim($_GET['url']);
	
	if (empty($url)) {
		exit('请输入网站域名！');
	} else {
		if (!is_valid_domain($url)) {
			exit('请输入正确的网站域名！');
		}
	}
			
	$url = mysqli_real_escape_string($DB->db_link, $url);
	$query = $DB->query("SELECT web_id, web_name, web_status, web_ctime FROM ".$DB->table('websites')." WHERE web_url='$url'");
	if ($DB->num_rows($query)) {
		$row = $DB->fetch_array($query);
		$status_text = '';
		$status_color = '';

		switch($row['web_status']) {
			case 1:
				$status_text = '该网站已被拉黑，无法提交';
				$status_color = '#333';
				break;
			case 2:
				$status_text = '该网站正在审核中，请勿重复提交';
				$status_color = '#ff6600';
				break;
			case 3:
				$status_text = '该网站已收录（' . date('Y-m-d', $row['web_ctime']) . '）';
				$status_color = '#f00';
				break;
			case 4:
				$status_text = '该网站审核不通过，请修改后重新提交';
				$status_color = '#f60';
				break;
			default:
				$status_text = '该域名已存在，请勿重复提交';
				$status_color = '#f00';
		}

		echo('<span style="color: ' . $status_color . '; font-weight: bold;">' . $status_text . '</span>');
	} else {
		echo('<span style="color: #090; font-weight: bold;">✓ 该域名可以提交</span> <a href="javascript: void(0);" onclick="getmeta(\''.$url.'\'); getrank(\''.$url.'\')">自动抓取&raquo;</a>');
	}
	$DB->free_result($query);
}

/** crawl */
if ($type == 'crawl') {
	$url = trim($_GET['url']);
	if (empty($url)) {
		exit('请输入网站域名！');
	} else {
		if (!is_valid_domain($url)) {
			exit('请输入正确的网站域名！');
		}
	}
	
	$meta = get_sitemeta($url);
	echo '<script type="text/javascript">';
	echo '$("#web_name").attr("value", "'.addslashes($meta['title']).'");';
	echo '$("#web_tags").attr("value", "'.addslashes($meta['keywords']).'");';
	echo '$("#web_intro").attr("value", "'.addslashes($meta['description']).'");';
	echo '</script>';
	unset($meta);
}


/** data */
if ($type == 'data') {
	$url = trim($_GET['url']);
	if (empty($url)) {
		echo '<script type="text/javascript">';
		echo '$("#data_btn").val("重新获取").prop("disabled", false);';
		echo 'alert("请输入网站域名！");';
		echo '</script>';
		exit;
	} else {
		if (!is_valid_domain($url)) {
			echo '<script type="text/javascript">';
			echo '$("#data_btn").val("重新获取").prop("disabled", false);';
			echo 'alert("请输入正确的网站域名！");';
			echo '</script>';
			exit;
		}
	}

	// 清理输出缓冲区
	if (ob_get_level()) {
		ob_clean();
	}

	// 设置内容类型和禁用缓存
	header('Content-Type: text/html; charset=utf-8');
	header('Cache-Control: no-cache, must-revalidate');
	header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');

	// 立即输出开始状态
	echo '<script type="text/javascript">';
	echo 'console.log("开始获取数据: ' . $url . '");';
	echo '</script>';

	// 强制输出缓冲区
	if (ob_get_level()) {
		ob_flush();
	}
	flush();

	try {
		// 设置更长的执行时间
		set_time_limit(120);

		// 记录开始时间
		$start_total = microtime(true);

		// 逐步获取数据并实时反馈
		echo '<script type="text/javascript">';
		echo 'console.log("正在获取服务器IP...");';
		echo '</script>';
		if (ob_get_level()) ob_flush(); flush();

		$ip = get_serverip($url);
		$ip = $ip ? $ip : '获取失败';

		echo '<script type="text/javascript">';
		echo 'var ipField = $("#web_ip"); console.log("IP字段找到:", ipField.length);';
		echo 'ipField.val("'.$ip.'"); console.log("IP设置后的值:", ipField.val());';
		echo 'console.log("IP获取完成: ' . $ip . '");';
		echo '</script>';
		if (ob_get_level()) ob_flush(); flush();

		echo '<script type="text/javascript">';
		echo 'console.log("正在获取百度收录量...");';
		echo '</script>';
		if (ob_get_level()) ob_flush(); flush();

		$grank = get_pagerank($url);
		$grank = is_numeric($grank) ? $grank : 0;

		echo '<script type="text/javascript">';
		echo 'var grankField = $("#web_grank"); console.log("百度收录字段找到:", grankField.length);';
		echo 'grankField.val("'.$grank.'"); console.log("百度收录设置后的值:", grankField.val());';
		echo 'console.log("百度收录量获取完成: ' . $grank . '");';
		echo '</script>';
		if (ob_get_level()) ob_flush(); flush();

		echo '<script type="text/javascript">';
		echo 'console.log("正在获取必应收录量...");';
		echo '</script>';
		if (ob_get_level()) ob_flush(); flush();

		$brank = get_baidurank($url);
		$brank = is_numeric($brank) ? $brank : 0;

		echo '<script type="text/javascript">';
		echo 'var brankField = $("#web_brank"); console.log("必应收录字段找到:", brankField.length);';
		echo 'brankField.val("'.$brank.'"); console.log("必应收录设置后的值:", brankField.val());';
		echo 'console.log("必应收录量获取完成: ' . $brank . '");';
		echo '</script>';
		if (ob_get_level()) ob_flush(); flush();

		echo '<script type="text/javascript">';
		echo 'console.log("正在获取360收录量...");';
		echo '</script>';
		if (ob_get_level()) ob_flush(); flush();

		$srank = get_sogourank($url);
		$srank = is_numeric($srank) ? $srank : 0;

		echo '<script type="text/javascript">';
		echo 'var srankField = $("#web_srank"); console.log("360收录字段找到:", srankField.length);';
		echo 'srankField.val("'.$srank.'"); console.log("360收录设置后的值:", srankField.val());';
		echo 'console.log("360收录量获取完成: ' . $srank . '");';
		echo '</script>';
		if (ob_get_level()) ob_flush(); flush();

		echo '<script type="text/javascript">';
		echo 'console.log("正在获取搜狗收录量...");';
		echo '</script>';
		if (ob_get_level()) ob_flush(); flush();

		$arank = get_alexarank($url);
		$arank = is_numeric($arank) ? $arank : 0;

		// 计算总耗时
		$total_time = round((microtime(true) - $start_total) * 1000, 2);

		echo '<script type="text/javascript">';
		echo 'var arankField = $("#web_arank"); console.log("搜狗收录字段找到:", arankField.length);';
		echo 'arankField.val("'.$arank.'"); console.log("搜狗收录设置后的值:", arankField.val());';
		echo '$("#data_btn").val("重新获取").prop("disabled", false);';
		echo 'console.log("搜狗收录量获取完成: ' . $arank . '");';
		echo 'console.log("数据获取完成 (总耗时: '.$total_time.'ms): IP='.$ip.', 百度='.$grank.', 必应='.$brank.', 360='.$srank.', 搜狗='.$arank.'");';
		echo 'alert("数据获取完成！\\n\\nIP: '.$ip.'\\n百度收录: '.$grank.'\\n必应收录: '.$brank.'\\n360收录: '.$srank.'\\n搜狗收录: '.$arank.'\\n\\n总耗时: '.$total_time.'ms");';
		echo '</script>';

	} catch (Exception $e) {
		echo '<script type="text/javascript">';
		echo '$("#data_btn").val("重新获取").prop("disabled", false);';
		echo 'alert("获取数据时发生错误: '.addslashes($e->getMessage()).'");';
		echo 'console.error("获取数据错误: '.addslashes($e->getMessage()).'");';
		echo '</script>';
	}
}

/** 获取音乐列表 */
if ($type == 'music_list') {
	header('Content-Type: application/json; charset=utf-8');

	try {
		// 获取所有音乐分类的音乐外链（包括流行歌曲、DJ串烧等）
		$music_links = get_music_links(null, 20);

		// 调试信息：检查数据库中的文章情况
		$debug_info = array();
		$music_categories = array(317, 318, 319); // 音乐试听专区、流行歌曲、DJ串烧
		$category_condition = "cate_id IN (" . implode(',', $music_categories) . ")";

		// 检查所有音乐分类下的文章总数
		$total_sql = "SELECT COUNT(*) as total FROM ".$DB->table('articles')." WHERE $category_condition";
		$total_result = $DB->query($total_sql);
		$total_row = $DB->fetch_array($total_result);
		$debug_info['total_articles'] = $total_row['total'];

		// 检查包含MP3的文章数
		$mp3_sql = "SELECT COUNT(*) as total FROM ".$DB->table('articles')." WHERE $category_condition AND art_content LIKE '%.mp3%'";
		$mp3_result = $DB->query($mp3_sql);
		$mp3_row = $DB->fetch_array($mp3_result);
		$debug_info['mp3_articles'] = $mp3_row['total'];

		// 检查已审核且包含音乐内容的文章数（包括管理员待审核）
		$approved_sql = "SELECT COUNT(*) as total FROM ".$DB->table('articles')." WHERE $category_condition AND (art_content LIKE '%.mp3%' OR art_content LIKE '%music.163.com%' OR art_content LIKE '%y.qq.com%' OR art_content LIKE '%kugou.com%' OR art_content LIKE '%audio%' OR art_content LIKE '%音乐%' OR art_content LIKE '%歌曲%' OR art_content LIKE '%DJ%' OR art_content LIKE '%串烧%') AND (art_status=3 OR (user_id=1 AND art_status=2))";
		$approved_result = $DB->query($approved_sql);
		$approved_row = $DB->fetch_array($approved_result);
		$debug_info['approved_music_articles'] = $approved_row['total'];

		// 按分类统计
		foreach ($music_categories as $cate_id) {
			$cate_sql = "SELECT COUNT(*) as total FROM ".$DB->table('articles')." WHERE cate_id=$cate_id AND (art_status=3 OR (user_id=1 AND art_status=2))";
			$cate_result = $DB->query($cate_sql);
			$cate_row = $DB->fetch_array($cate_result);
			$debug_info["category_$cate_id"] = $cate_row['total'];
		}

		if (empty($music_links)) {
			// 如果没有找到音乐链接，尝试从单个分类获取
			$fallback_links = array();

			// 依次尝试各个音乐分类
			foreach ($music_categories as $cate_id) {
				$single_links = get_music_links($cate_id, 5);
				if (!empty($single_links)) {
					$fallback_links = array_merge($fallback_links, $single_links);
				}
				if (count($fallback_links) >= 10) break;
			}

			// 如果还是没有，返回示例音乐
			if (empty($fallback_links)) {
				$fallback_links = array(
					array(
						'title' => '示例音乐 1 - 周杰伦《青花瓷》',
						'url' => 'https://music.163.com/song/media/outer/url?id=185668',
						'art_id' => 0,
						'ctime' => time()
					),
					array(
						'title' => '示例音乐 2 - 邓紫棋《泡沫》',
						'url' => 'https://music.163.com/song/media/outer/url?id=287025',
						'art_id' => 0,
						'ctime' => time()
					),
					array(
						'title' => '示例音乐 3 - 林俊杰《江南》',
						'url' => 'https://music.163.com/song/media/outer/url?id=185817',
						'art_id' => 0,
						'ctime' => time()
					),
					array(
						'title' => '示例音乐 4 - 陈奕迅《十年》',
						'url' => 'https://music.163.com/song/media/outer/url?id=185668',
						'art_id' => 0,
						'ctime' => time()
					),
					array(
						'title' => '示例音乐 5 - 王菲《红豆》',
						'url' => 'https://music.163.com/song/media/outer/url?id=185692',
						'art_id' => 0,
						'ctime' => time()
					)
				);
			}

			echo json_encode(array(
				'success' => true,
				'music_list' => $fallback_links,
				'count' => count($fallback_links),
				'debug' => $debug_info,
				'message' => '使用备用音乐数据'
			));
		} else {
			echo json_encode(array(
				'success' => true,
				'music_list' => $music_links,
				'count' => count($music_links),
				'debug' => $debug_info
			));
		}

	} catch (Exception $e) {
		echo json_encode(array(
			'success' => false,
			'error' => '获取音乐列表失败: ' . $e->getMessage()
		));
	}
	exit;
}
?>