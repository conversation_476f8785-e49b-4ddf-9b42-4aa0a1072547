<?php
// 测试音乐链接序号是否正确
define('IN_IWEBDIR', true);
define('APP_PATH', './source/');

require('./source/init.php');
require('./source/module/article.php');

echo "<h2>测试音乐链接序号</h2>\n";

// 创建一个包含多个音乐链接的测试文章
$test_content = '
这是一篇包含多个音乐链接的测试文章：

第一首歌：https://music.163.com/song/media/outer/url?id=123456
第二首歌：https://example.com/music/song2.mp3
第三首歌：<audio src="https://music.example.com/song3.mp3" controls></audio>
第四首歌：https://y.qq.com/n/yqq/song/004567.html
第五首歌：<source src="https://cdn.music.com/song5.mp3" type="audio/mpeg">
第六首歌：https://www.kugou.com/song/song6.html

这些链接应该按顺序编号为 1、2、3、4、5、6
';

echo "<h3>1. 测试链接提取</h3>\n";
$extracted_links = extract_music_urls($test_content);
echo "<p>提取到的链接数量: " . count($extracted_links) . "</p>\n";
echo "<ol>\n";
foreach ($extracted_links as $index => $link) {
    echo "<li>索引 $index: " . htmlspecialchars($link) . "</li>\n";
}
echo "</ol>\n";

// 删除现有的测试文章
$DB->query("DELETE FROM ".$DB->table('articles')." WHERE art_title LIKE '序号测试%'");

// 添加测试文章
$title = $DB->escape_string('序号测试 - 多链接文章');
$content = $DB->escape_string($test_content);
$intro = $DB->escape_string('这是一篇用于测试音乐链接序号的文章');

$sql = "INSERT INTO ".$DB->table('articles')." 
        (user_id, cate_id, art_title, art_tags, art_intro, art_content, art_status, art_ctime, art_utime) 
        VALUES 
        (1, 318, '$title', '测试,音乐,序号', '$intro', '$content', 3, ".time().", ".time().")";

if ($DB->query($sql)) {
    $art_id = $DB->insert_id();
    echo "<h3>2. 测试文章创建成功 (ID: $art_id)</h3>\n";
    
    // 测试get_music_links函数
    echo "<h3>3. 测试get_music_links函数</h3>\n";
    $music_links = get_music_links(318, 20);
    
    echo "<p>获取到的音乐链接:</p>\n";
    echo "<ul>\n";
    foreach ($music_links as $link) {
        if (strpos($link['title'], '序号测试') !== false) {
            echo "<li><strong>" . htmlspecialchars($link['title']) . "</strong><br>";
            echo "URL: " . htmlspecialchars($link['url']) . "</li>\n";
        }
    }
    echo "</ul>\n";
    
    // 测试AJAX接口
    echo "<h3>4. 测试AJAX接口</h3>\n";
    echo "<p><a href='?mod=ajaxget&type=music_list' target='_blank'>点击查看AJAX接口返回的数据</a></p>\n";
    
} else {
    echo "<p style='color: red;'>创建测试文章失败: " . $DB->error() . "</p>\n";
}

echo "<h3>5. 预期结果</h3>\n";
echo "<p>如果修复成功，你应该看到以下格式的标题:</p>\n";
echo "<ul>\n";
echo "<li>序号测试 - 多链接文章 1 [流行歌曲]</li>\n";
echo "<li>序号测试 - 多链接文章 2 [流行歌曲]</li>\n";
echo "<li>序号测试 - 多链接文章 3 [流行歌曲]</li>\n";
echo "<li>序号测试 - 多链接文章 4 [流行歌曲]</li>\n";
echo "<li>序号测试 - 多链接文章 5 [流行歌曲]</li>\n";
echo "<li>序号测试 - 多链接文章 6 [流行歌曲]</li>\n";
echo "</ul>\n";

echo "<p><strong>注意:</strong> 序号应该是连续的 1、2、3、4、5、6，而不是跳跃的 1、3、5 等。</p>\n";
?>
